package com.github.b4ndithelps.tennogamenolife.race.Dwarf;

import com.github.b4ndithelps.tennogamenolife.registry.race.Tenreincarnationraces;
import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.github.manasmods.tensura.util.JumpPowerHelper;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.mojang.datafixers.util.Pair;
import net.minecraft.world.entity.player.Player;
import net.minecraft.network.chat.Component;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Nullable;

/**
 * 地精种族
 */
public class DwarfRace extends Race {

    // 种族基础属性
    private double baseHealth = 15.0;          // 基础生命值
    private double baseAttackDamage = 1.0;     // 基础攻击伤害
    private double baseAttackSpeed = 4.0;      // 基础攻击速度
    private double knockbackResistance = 0.5;   // 击退抗性
    private double jumpHeight = 1.0;           // 跳跃高度
    private double movementSpeed = 0.1;        // 移动速度
    private double sprintSpeed = 0.15;         // 冲刺速度
    private double auraMin = 800.0;           // 最小灵气值
    private double auraMax = 1500.0;           // 最大灵气值
    private double startingMagiculeMin = 150.0; // 最小初始魔素值
    private double startingMagiculeMax = 350.0; // 最大初始魔素值

    private float playerSize = 1.5f;

    public DwarfRace() {
        super(Difficulty.INTERMEDIATE);
    }

    @Override
    public double getBaseHealth() {
        return baseHealth;
    }

    @Override
    public float getPlayerSize() {
        return playerSize;
    }

    @Override
    public double getBaseAttackDamage() {
        return baseAttackDamage;
    }

    @Override
    public double getBaseAttackSpeed() {
        return baseAttackSpeed;
    }

    @Override
    public double getKnockbackResistance() {
        return knockbackResistance;
    }

    @Override
    public double getJumpHeight() {
        return JumpPowerHelper.defaultPlayer(jumpHeight);
    }

    @Override
    public double getMovementSpeed() {
        return movementSpeed;
    }

    @Override
    public double getSprintSpeed() {
        return sprintSpeed;
    }

    @Override
    public Pair<Double, Double> getBaseAuraRange() {
        return Pair.of(auraMin, auraMax);
    }

    @Override
    public Pair<Double, Double> getBaseMagiculeRange() {
        return Pair.of(startingMagiculeMin, startingMagiculeMax);
    }

    @Override
    public List<TensuraSkill> getIntrinsicSkills(Player player) {
        List<TensuraSkill> list = new ArrayList<>();
        list.add(IntrinsicSkills.ABSORB_DISSOLVE.get());
        list.add(ResistanceSkills.PHYSICAL_ATTACK_RESISTANCE.get());
        list.add(ResistanceSkills.MAGIC_RESISTANCE.get());
        return list;
    }

    @Override
    public boolean isMajin() {
        return false;
    }

    @Override
    public boolean isSpiritual() {
        return false;
    }

    @Override
    public boolean isDivine() {
        return false;
    }

    @Override
    public void raceAbility(Player player) {
    }

    @Override
    public List<Race> getNextEvolutions(Player player) {
        List<Race> list = new ArrayList<>();
        list.add(Tenreincarnationraces.NII_TIRUVIRUGU.get());
        return list;
    }

    @Nullable
    @Override
    public Race getDefaultEvolution(Player player) {
        return Tenreincarnationraces.NII_TIRUVIRUGU.get();
    }

    @Nullable
    @Override
    public Race getAwakeningEvolution(Player player) {
        return null;
    }

    @Nullable
    @Override
    public Race getHarvestFestivalEvolution(Player player) {
        return null;
    }
}