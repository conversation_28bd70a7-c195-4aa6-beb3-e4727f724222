package com.github.b4ndithelps.tennogamenolife.race.Doragonia;

import com.github.b4ndithelps.tennogamenolife.registry.race.Tenreincarnationraces;
import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.github.manasmods.tensura.util.JumpPowerHelper;
import com.mojang.datafixers.util.Pair;
import net.minecraft.world.entity.player.Player;
import net.minecraft.network.chat.Component;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Nullable;

/**
 * 下级龙
 */
public class LesserDragoniaRace extends Race {

    // 种族基础属性
    private double baseHealth = 40.0;          // 基础生命值
    private double baseAttackDamage = 2.0;     // 基础攻击伤害
    private double baseAttackSpeed = 4.0;      // 基础攻击速度
    private double knockbackResistance = 0.5;  // 击退抗性
    private double jumpHeight = 1.0;           // 跳跃高度
    private double movementSpeed = 0.11;       // 移动速度
    private double sprintSpeed = 0.14;         // 冲刺速度
    private double auraMin = 1200.0;           // 最小灵气值
    private double auraMax = 2000.0;           // 最大灵气值
    private double startingMagiculeMin = 500.0; // 最小初始魔素值
    private double startingMagiculeMax = 800.0; // 最大初始魔素值

    private float playerSize = 2.3f;           // 玩家大小

    public LesserDragoniaRace() {
        super(Difficulty.EASY);
    }

    @Override
    public double getBaseHealth() {
        return baseHealth;
    }

    @Override
    public float getPlayerSize() {
        return playerSize;
    }

    @Override
    public double getBaseAttackDamage() {
        return baseAttackDamage;
    }

    @Override
    public double getBaseAttackSpeed() {
        return baseAttackSpeed;
    }

    @Override
    public double getKnockbackResistance() {
        return knockbackResistance;
    }

    @Override
    public double getJumpHeight() {
        return JumpPowerHelper.defaultPlayer(jumpHeight);
    }

    @Override
    public double getMovementSpeed() {
        return movementSpeed;
    }

    @Override
    public double getSprintSpeed() {
        return sprintSpeed;
    }

    @Override
    public Pair<Double, Double> getBaseAuraRange() {
        return Pair.of(auraMin, auraMax);
    }

    @Override
    public Pair<Double, Double> getBaseMagiculeRange() {
        return Pair.of(startingMagiculeMin, startingMagiculeMax);
    }

    @Override
    public List<TensuraSkill> getIntrinsicSkills(Player player) {
        List<TensuraSkill> list = new ArrayList<>();
        list.add(IntrinsicSkills.SCALE_ARMOR.get());
        return list;
    }

    @Override
    public boolean isMajin() {
        return false;
    }

    @Override
    public boolean isSpiritual() {
        return false;
    }

    @Override
    public boolean isDivine() {
        return false;
    }

    @Override
    public void raceAbility(Player player) {
        // 暂不实现种族能力
    }

    @Override
    public double getEvolutionPercentage(Player player) {
        return TensuraPlayerCapability.getBaseEP(player) * 100.0D / 22000.0D;
    }

    @Override
    public List<Component> getRequirementsForRendering(Player player) {
        List<Component> list = new ArrayList<>();
        list.add(Component.translatable("tensura.evolution_menu.ep_requirement"));
        return list;
    }

    @Override
    public List<Race> getNextEvolutions(Player player) {
        List<Race> list = new ArrayList<>();
        list.add(Tenreincarnationraces.MIDDLE_DRAGONIA.get());
        return list;
    }

    @Nullable
    @Override
    public Race getDefaultEvolution(Player player) {
        return Tenreincarnationraces.MIDDLE_DRAGONIA.get();
    }

    @Nullable
    @Override
    public Race getAwakeningEvolution(Player player) {
        return null; // 暂无觉醒进化
    }

    @Nullable
    @Override
    public Race getHarvestFestivalEvolution(Player player) {
        return null; // 暂无丰收节进化
    }
} 