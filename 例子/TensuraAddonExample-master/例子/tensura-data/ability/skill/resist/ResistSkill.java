package com.github.manasmods.tensura.ability.skill.resist;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.event.UnlockSkillEvent;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.github.manasmods.tensura.util.damage.TensuraDamageSource;
import java.util.List;
import java.util.Objects;
import javax.annotation.Nullable;
import net.minecraft.ChatFormatting;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.entity.living.LivingAttackEvent;
import net.minecraftforge.event.entity.living.LivingDamageEvent;

public class ResistSkill extends Skill {
   private final ResistSkill.ResistType resistType;

   public ResistSkill(Skill.SkillType skillType, ResistSkill.ResistType resistType) {
      super(skillType);
      this.resistType = resistType;
   }

   public ResistSkill(ResistSkill.ResistType resistType) {
      super(Skill.SkillType.RESISTANCE);
      this.resistType = resistType;
   }

   public ResistSkill() {
      this(Skill.SkillType.RESISTANCE, ResistSkill.ResistType.RESISTANCE);
   }

   public boolean canInteractSkill(ManasSkillInstance instance, LivingEntity entity) {
      if (entity.m_21023_((MobEffect)TensuraMobEffects.SLEEP_MODE.get())) {
         return false;
      } else if (entity.m_21023_((MobEffect)TensuraMobEffects.REST.get())) {
         return false;
      } else if (entity.m_21023_((MobEffect)TensuraMobEffects.INFINITE_IMPRISONMENT.get())) {
         return false;
      } else {
         return !SkillUtils.noInteractiveMode(entity);
      }
   }

   public boolean canBeSlotted(ManasSkillInstance instance) {
      return false;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return instance.getMastery() >= 0;
   }

   public void onToggleOn(ManasSkillInstance skillInstance, LivingEntity living) {
      List<MobEffect> list = this.getImmuneEffects(skillInstance, living);
      if (!list.isEmpty()) {
         Objects.requireNonNull(list);
         SkillHelper.removePredicateEffect(living, list::contains);
      }
   }

   public boolean isNullificationBypass(DamageSource damageSource) {
      Entity var3 = damageSource.m_7639_();
      if (var3 instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)var3;
         if (TensuraSkillCapability.isSkillInSlot(living, (ManasSkill)UniqueSkills.ANTI_SKILL.get())) {
            return true;
         } else {
            boolean var10000;
            if (damageSource instanceof TensuraDamageSource) {
               TensuraDamageSource source = (TensuraDamageSource)damageSource;
               if (source.getIgnoreResistance() >= 2.0F) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         }
      } else {
         return false;
      }
   }

   public boolean isResistanceBypass(DamageSource damageSource) {
      Entity var3 = damageSource.m_7639_();
      if (var3 instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)var3;
         if (damageSource instanceof TensuraDamageSource) {
            TensuraDamageSource source = (TensuraDamageSource)damageSource;
            if (source.getIgnoreResistance() == 1.0F) {
               return true;
            }
         }

         return SkillUtils.reducingResistances(living);
      } else {
         return false;
      }
   }

   public boolean isDamageResisted(DamageSource damageSource, ManasSkillInstance instance) {
      return false;
   }

   protected double getHpMultiplierForResistance(boolean nullification) {
      return nullification ? (Double)TensuraConfig.INSTANCE.skillsConfig.hpForNullification.get() : (Double)TensuraConfig.INSTANCE.skillsConfig.hpForResistance.get();
   }

   protected double getResistanceDamageMultiplier(boolean nullification) {
      return nullification ? (Double)TensuraConfig.INSTANCE.skillsConfig.nullifcationDamageMultiplier.get() : (Double)TensuraConfig.INSTANCE.skillsConfig.resistanceDamageMultiplier.get();
   }

   public void onBeingDamaged(ManasSkillInstance instance, LivingAttackEvent event) {
      if (!event.isCanceled()) {
         if (instance.isToggled()) {
            if (this.resistType.equals(ResistSkill.ResistType.NULLIFICATION)) {
               if (!(this.getResistanceDamageMultiplier(true) > 0.0D)) {
                  if (!(this.getHpMultiplierForResistance(true) >= 0.0D)) {
                     DamageSource source = event.getSource();
                     if (!this.isNullificationBypass(source)) {
                        if (this.isDamageResisted(source, instance)) {
                           if (!this.isResistanceBypass(source)) {
                              event.setCanceled(true);
                           }

                        }
                     }
                  }
               }
            }
         }
      }
   }

   public void onTakenDamage(ManasSkillInstance instance, LivingDamageEvent event) {
      if (!event.isCanceled()) {
         if (instance.getMastery() < 0) {
            if (this.isDamageResisted(event.getSource(), instance)) {
               if (this.resistType.equals(ResistSkill.ResistType.RESISTANCE)) {
                  if ((double)event.getAmount() > this.learningCost()) {
                     this.addLearnPoint(instance, event.getEntity());
                  }

               }
            }
         } else if (instance.isToggled()) {
            if (this.isDamageResisted(event.getSource(), instance)) {
               boolean bypass = this.isResistanceBypass(event.getSource());
               if (this.resistType.equals(ResistSkill.ResistType.NULLIFICATION)) {
                  double hpMultiplier;
                  float multiplier;
                  if (bypass) {
                     hpMultiplier = this.getHpMultiplierForResistance(false);
                     if (!(hpMultiplier < 0.0D) && !((double)event.getAmount() < (double)event.getEntity().m_21223_() * hpMultiplier)) {
                        multiplier = (float)this.getResistanceDamageMultiplier(false);
                        if (multiplier <= 0.0F) {
                           event.setCanceled(true);
                        } else {
                           event.setAmount(event.getAmount() * multiplier);
                        }
                     } else {
                        event.setCanceled(true);
                     }
                  } else {
                     hpMultiplier = this.getHpMultiplierForResistance(true);
                     if (!(hpMultiplier < 0.0D) && !((double)event.getAmount() < (double)event.getEntity().m_21223_() * hpMultiplier)) {
                        multiplier = (float)this.getResistanceDamageMultiplier(true);
                        if (multiplier <= 0.0F) {
                           event.setCanceled(true);
                        } else {
                           event.setAmount(event.getAmount() * multiplier);
                        }
                     } else {
                        event.setCanceled(true);
                     }
                  }
               } else if (this.resistType.equals(ResistSkill.ResistType.RESISTANCE) && !bypass) {
                  ManasSkill nullification = this.getNullificationForm();
                  if (nullification != null && SkillUtils.isSkillToggled(event.getEntity(), nullification)) {
                     return;
                  }

                  double hpMultiplier = this.getHpMultiplierForResistance(false);
                  if (!(hpMultiplier < 0.0D) && !((double)event.getAmount() < (double)event.getEntity().m_21223_() * hpMultiplier)) {
                     float multiplier = (float)this.getResistanceDamageMultiplier(false);
                     if (multiplier <= 0.0F) {
                        event.setCanceled(true);
                     } else {
                        event.setAmount(event.getAmount() * multiplier);
                     }
                  } else {
                     event.setCanceled(true);
                  }
               }

            }
         }
      }
   }

   public double learningCost() {
      return 5.0D;
   }

   public int pointRequirement() {
      return 100;
   }

   public void addLearnPoint(ManasSkillInstance instance, LivingEntity entity) {
      if (instance.getMastery() < 0) {
         int oldMastery = instance.getMastery();
         int newMastery = oldMastery + SkillUtils.getEarningLearnPoint(instance, entity, false);
         instance.setMastery(Math.min(newMastery, 0));
         instance.markDirty();
         if (oldMastery < 0 && newMastery >= 0) {
            UnlockSkillEvent event = new UnlockSkillEvent(instance, entity);
            if (MinecraftForge.EVENT_BUS.post(event)) {
               instance.setMastery(oldMastery);
               instance.markDirty();
               return;
            }

            if (entity instanceof Player) {
               Player player = (Player)entity;
               player.m_5661_(Component.m_237110_("tensura.skill.acquire_learning", new Object[]{this.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
            }

            instance.onLearnSkill(entity, event);
            instance.setToggled(true);
            instance.onToggleOn(entity);
         }
      }

   }

   public void evolveToNullification(ManasSkillInstance instance, LivingEntity entity) {
      if (instance.getMastery() >= 0) {
         if (!instance.isTemporarySkill()) {
            ManasSkill nullification = this.getNullificationForm();
            if (nullification != null) {
               if (SkillUtils.learnSkill(entity, nullification) && entity instanceof Player) {
                  Player player = (Player)entity;
                  TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
                     cap.addIntrinsicSkill(nullification);
                     TensuraPlayerCapability.sync(player);
                  });
                  player.m_5661_(Component.m_237110_("tensura.skill.acquire", new Object[]{nullification.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
               }

            }
         }
      }
   }

   @Nullable
   protected ManasSkill getNullificationForm() {
      return null;
   }

   public ResistSkill.ResistType getResistType() {
      return this.resistType;
   }

   public static enum ResistType {
      RESISTANCE,
      NULLIFICATION;

      // $FF: synthetic method
      private static ResistSkill.ResistType[] $values() {
         return new ResistSkill.ResistType[]{RESISTANCE, NULLIFICATION};
      }
   }
}
