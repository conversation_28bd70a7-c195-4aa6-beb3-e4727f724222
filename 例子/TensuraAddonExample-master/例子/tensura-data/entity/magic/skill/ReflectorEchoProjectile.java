package com.github.manasmods.tensura.entity.magic.skill;

import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.entity.magic.TensuraProjectile;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.Optional;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.level.Level;

public class ReflectorEchoProjectile extends TensuraProjectile {
   protected static final ResourceLocation[] TEXTURES = new ResourceLocation[]{new ResourceLocation("tensura", "textures/entity/projectiles/lightning_ball/yellow_lightning_ball_0.png"), new ResourceLocation("tensura", "textures/entity/projectiles/lightning_ball/yellow_lightning_ball_1.png"), new ResourceLocation("tensura", "textures/entity/projectiles/lightning_ball/yellow_lightning_ball_2.png"), new ResourceLocation("tensura", "textures/entity/projectiles/lightning_ball/yellow_lightning_ball_3.png"), new ResourceLocation("tensura", "textures/entity/projectiles/lightning_ball/yellow_lightning_ball_4.png"), new ResourceLocation("tensura", "textures/entity/projectiles/lightning_ball/yellow_lightning_ball_5.png")};

   public ReflectorEchoProjectile(EntityType<? extends ReflectorEchoProjectile> entityType, Level level) {
      super(entityType, level);
   }

   public ReflectorEchoProjectile(Level levelIn, LivingEntity shooter) {
      super((EntityType)TensuraEntityTypes.REFLECTOR_ECHO.get(), levelIn);
      this.m_5602_(shooter);
   }

   public boolean shouldDiscardInWater() {
      return false;
   }

   public boolean shouldDiscardInLava() {
      return false;
   }

   public boolean piercingEntity() {
      return true;
   }

   public ResourceLocation[] getTextureLocation() {
      return TEXTURES;
   }

   public void setPosAndShoot(LivingEntity entity) {
      this.m_146884_(entity.m_20182_().m_82520_(0.0D, (double)entity.m_20192_(), 0.0D));
      this.shootFromRot(entity.m_20154_());
   }

   protected void dealDamage(Entity target) {
      DamageSource damageSource = TensuraDamageSources.reflectorEcho(this, this.m_37282_());
      if (this.damage > 0.0F) {
         target.m_6469_(DamageSourceHelper.addSkillAndCost(damageSource, this.getMpCost(), this.getSkill()), this.getDamage());
      }

   }

   public Optional<SoundEvent> hitSound() {
      return Optional.of(SoundEvents.f_11913_);
   }

   public void hitParticles(double x, double y, double z) {
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, ParticleTypes.f_123813_, x, y, z, 1, 0.12D, 0.12D, 0.12D, 0.15D, false);
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, ParticleTypes.f_123747_, x, y, z, 3, 0.5D, 0.5D, 0.5D, 0.1D, false);
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, (ParticleOptions)TensuraParticles.YELLOW_LIGHTNING_SPARK.get(), x, y, z, 10, 0.5D, 0.5D, 0.5D, 0.2D, false);
   }

   public void flyingParticles() {
      if ((double)this.f_19796_.m_188501_() <= 0.8D) {
         double dx = this.f_19853_.f_46441_.m_188500_() * 0.05D - 0.05D;
         double dy = this.f_19853_.f_46441_.m_188500_() * 0.05D - 0.05D;
         double dz = this.f_19853_.f_46441_.m_188500_() * 0.05D - 0.05D;
         double x = (this.f_19853_.f_46441_.m_188500_() - 0.5D) * 4.0D;
         double y = (this.f_19853_.f_46441_.m_188500_() - 0.5D) * 4.0D;
         double z = (this.f_19853_.f_46441_.m_188500_() - 0.5D) * 4.0D;
         this.f_19853_.m_7106_((ParticleOptions)TensuraParticles.YELLOW_LIGHTNING_SPARK.get(), this.m_20185_() + x, this.m_20186_() + y, this.m_20189_() + z, dx, dy, dz);
      }

   }
}
