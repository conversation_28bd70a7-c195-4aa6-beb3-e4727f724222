package com.github.manasmods.tensura.enchantment;

import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.MobType;
import net.minecraft.world.entity.EquipmentSlot.Type;
import net.minecraft.world.item.DiggerItem;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.SwordItem;
import net.minecraft.world.item.enchantment.EnchantmentCategory;
import net.minecraft.world.item.enchantment.Enchantment.Rarity;
import net.minecraftforge.common.ToolActions;

public class CrushingEnchantment extends EngravingEnchantment {
   public CrushingEnchantment() {
      super(Rarity.VERY_RARE, EnchantmentCategory.WEAPON, EquipmentSlot.MAINHAND);
   }

   public float getDamageBonus(int level, MobType mobType, ItemStack enchantedItem) {
      float damage = 10.0F * (float)level;
      Item var6 = enchantedItem.m_41720_();
      if (var6 instanceof SwordItem) {
         SwordItem sword = (SwordItem)var6;
         damage = sword.m_43299_() * (float)level;
      }

      var6 = enchantedItem.m_41720_();
      if (var6 instanceof DiggerItem) {
         DiggerItem digger = (DiggerItem)var6;
         damage = digger.m_41008_() * (float)level;
      }

      return damage * 0.5F;
   }

   public void m_7677_(LivingEntity pAttacker, Entity pTarget, int pLevel) {
      if (pTarget instanceof LivingEntity) {
         LivingEntity target = (LivingEntity)pTarget;
         int durabilityBreak = 5 * pLevel;
         EquipmentSlot[] var6 = EquipmentSlot.values();
         int var7 = var6.length;

         for(int var8 = 0; var8 < var7; ++var8) {
            EquipmentSlot slot = var6[var8];
            if (!slot.m_20743_().equals(Type.HAND) || target.m_6844_(slot).canPerformAction(ToolActions.SHIELD_BLOCK)) {
               target.m_6844_(slot).m_41622_(durabilityBreak, target, (living) -> {
                  living.m_21166_(slot);
               });
            }
         }

      }
   }
}
