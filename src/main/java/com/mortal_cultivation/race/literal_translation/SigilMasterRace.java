package com.mortal_cultivation.race.literal_translation;

import com.mortal_cultivation.MortalCultivation;
import com.mortal_cultivation.registry.race.CultivationRaces;
import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.registry.race.TensuraRaces;
import com.github.manasmods.tensura.util.JumpPowerHelper;
import com.mojang.datafixers.util.Pair;
import net.minecraft.network.chat.Component;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.common.MinecraftForge;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.List;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import net.minecraft.ChatFormatting;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.phys.Vec3;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.core.particles.ParticleTypes;

/**
 * 符尊
 * 
 * 符尊是修真界中精通符箓之道的高阶修士，从符师晋升而来，掌握极高深的符箓制作与施放技巧。
 * 符尊能够御气腾空，实现二段跳，并且可以免疫8格以内的摔落伤害。
 */
public class SigilMasterRace extends Race {
    // 种族属性
    private double baseHealth = 55.0;                    // 基础生命值
    private double baseAttackDamage = 1.0;               // 基础攻击伤害
    private double baseAttackSpeed = 4.0;                // 基础攻击速度
    private double knockbackResistance = 0.05;           // 击退抗性
    private double jumpHeight = 0.1;                     // 跳跃高度
    private double movementSpeed = 0.11;                 // 移动速度
    private double sprintSpeed = 0.14;                   // 疾跑速度
    private double auraMin = 1300.0;                     // 灵力下限
    private double auraMax = 1800.0;                     // 灵力上限
    private double startingMagiculeMin = 120.0;          // 起始魔力下限
    private double startingMagiculeMax = 180.0;          // 起始魔力上限

    private float playerSize = 2.0f;                     // 玩家大小，标准大小
    
    // 二段跳能力相关变量
    private static final Map<UUID, Boolean> doubleJumpEnabled = new HashMap<>(); // 存储玩家的二段跳状态
    private static final Map<UUID, Boolean> hasDoubleJumped = new HashMap<>();  // 记录玩家是否已经二段跳过
    private static final float DOUBLE_JUMP_STRENGTH = 0.5f; // 二段跳的强度

    public SigilMasterRace() {
        // 符尊难度高
        super(Race.Difficulty.HARD);
    }
    
    @Override
    public double getBaseHealth() {
        return baseHealth;
    }

    @Override
    public float getPlayerSize() {
        return playerSize;
    }

    @Override
    public double getBaseAttackDamage() {
        return baseAttackDamage;
    }

    @Override
    public double getBaseAttackSpeed() {
        return baseAttackSpeed;
    }

    @Override
    public double getKnockbackResistance() {
        return knockbackResistance;
    }

    @Override
    public double getJumpHeight() {
        return JumpPowerHelper.defaultPlayer(jumpHeight);
    }

    @Override
    public double getMovementSpeed() {
        return movementSpeed;
    }

    @Override
    public double getSprintSpeed() {
        return sprintSpeed;
    }

    @Override
    public Pair<Double, Double> getBaseAuraRange() {
        return Pair.of(auraMin, auraMax);
    }

    @Override
    public Pair<Double, Double> getBaseMagiculeRange() {
        return Pair.of(startingMagiculeMin, startingMagiculeMax);
    }

    /**
     * 技能
     */
    @Override
    public List<TensuraSkill> getIntrinsicSkills(Player player) {
        List<TensuraSkill> list = new ArrayList<>();
        // 技能暂时留空
        return list;
    }
    
    @Nullable
    @Override
    public Race getDefaultEvolution(Player player) {
        return null; // 没有默认进化路线
    }
    
    @Nullable
    @Override
    public Race getAwakeningEvolution(Player player) {
        return null; // 没有觉醒进化路线
    }
    
    @Nullable
    @Override
    public Race getHarvestFestivalEvolution(Player player) {
        return null; // 没有丰收节进化路线
    }
    
    @Override
    public List<Race> getNextEvolutions(Player player) {
        List<Race> nextEvolutions = new ArrayList<>();
        nextEvolutions.add(CultivationRaces.GOLDEN_CORE_RACE.get());
        return nextEvolutions;
    }
    
    @Override
    public double getEvolutionPercentage(Player player) {
        // 初始化进化百分比
        double percentage = 0.0;
        
        // EP贡献的部分最多100%
        double epPercentage = Math.min(100.0, TensuraPlayerCapability.getBaseEP(player) * 100.0 / 20000.0);
        percentage += epPercentage;
        
        return percentage;
    }

    @Override
    public List<Component> getRequirementsForRendering(Player player) {
        List<Component> list = new ArrayList<>();
        
        // EP要求
        list.add(Component.translatable("tensura.evolution_menu.ep_requirement", 20000)
                .withStyle(ChatFormatting.AQUA));
        
        return list;
    }
    
    @Override
    public List<Race> getPreviousEvolutions(Player player) {
        List<Race> list = new ArrayList<>();
        list.add(com.mortal_cultivation.registry.race.CultivationRaces.TALISMAN_MASTER_RACE.get());
        return list;
    }

    /**
     * 种族特殊能力：二段跳
     * 
     * 符尊能够凝聚灵力于足下，在空中再次跳跃
     */
    @Override
    public void raceAbility(Player player) {
        if (player.isDeadOrDying() || player.isSleeping()) {
            return; // 玩家死亡或睡觉时不能使用
        }
        
        UUID playerId = player.getUUID();
        
        // 获取当前的二段跳状态
        boolean currentlyEnabled = doubleJumpEnabled.getOrDefault(playerId, false);
        
        // 切换状态
        if (currentlyEnabled) {
            // 关闭二段跳
            doubleJumpEnabled.put(playerId, false);
            
            // 提示信息
            player.displayClientMessage(
                Component.translatable("mortal_cultivation.ability.double_jump.disabled")
                    .withStyle(ChatFormatting.RED),
                true
            );
            
            // 播放关闭音效
            player.level.playSound(
                null, 
                player.getX(), 
                player.getY(), 
                player.getZ(), 
                SoundEvents.BEACON_DEACTIVATE, 
                SoundSource.PLAYERS, 
                0.6f, 
                1.2f
            );
        } else {
            // 开启二段跳
            doubleJumpEnabled.put(playerId, true);
            
            // 提示信息
            player.displayClientMessage(
                Component.translatable("mortal_cultivation.ability.double_jump.enabled")
                    .withStyle(ChatFormatting.GREEN),
                true
            );
            
            // 播放开启音效
            player.level.playSound(
                null, 
                player.getX(), 
                player.getY(), 
                player.getZ(), 
                SoundEvents.BEACON_ACTIVATE, 
                SoundSource.PLAYERS, 
                0.6f, 
                1.0f
            );
            
            // 添加粒子效果
            for (int i = 0; i < 20; i++) {
                player.level.addParticle(
                    ParticleTypes.CLOUD,
                    player.getX() + (player.getRandom().nextDouble() - 0.5) * 0.5,
                    player.getY(),
                    player.getZ() + (player.getRandom().nextDouble() - 0.5) * 0.5,
                    0, 0.1, 0
                );
            }
        }
    }

    /**
     * 检查玩家是否启用了二段跳能力
     * 
     * @param playerId 玩家UUID
     * @return 是否启用二段跳
     */
    public boolean isDoubleJumpEnabled(UUID playerId) {
        return doubleJumpEnabled.getOrDefault(playerId, false);
    }

    /**
     * 玩家已使用过二段跳
     * 
     * @param playerId 玩家UUID
     */
    public void setDoubleJumped(UUID playerId) {
        hasDoubleJumped.put(playerId, true);
    }

    /**
     * 重置玩家的二段跳状态（通常在玩家着陆时调用）
     * 
     * @param player 玩家对象
     */
    public void resetDoubleJump(Player player) {
        UUID playerId = player.getUUID();
        if (doubleJumpEnabled.getOrDefault(playerId, false)) {
            hasDoubleJumped.put(playerId, false);
        }
    }
    
    @Override
    public boolean isMajin() {
        return false;
    }

    @Override
    public boolean isSpiritual() {
        return true;
    }

    @Override
    public boolean isDivine() {
        return false;
    }
} 