package com.github.b4ndithelps.tennogamenolife.race.Doragonia;

import com.github.b4ndithelps.tennogamenolife.registry.race.Tenreincarnationraces;
import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.util.JumpPowerHelper;
import com.mojang.datafixers.util.Pair;
import net.minecraft.world.entity.player.Player;
import net.minecraft.network.chat.Component;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Nullable;

/**
 * 焉龙
 */
public class AranreivuRace extends Race {

    // 种族基础属性
    private double baseHealth = 1500.0;         // 基础生命值
    private double baseAttackDamage = 8.0;     // 基础攻击伤害
    private double baseAttackSpeed = 4.7;      // 基础攻击速度
    private double knockbackResistance = 0.85; // 击退抗性
    private double jumpHeight = 1.6;           // 跳跃高度
    private double movementSpeed = 0.17;       // 移动速度
    private double sprintSpeed = 0.22;         // 冲刺速度
    private double auraMin = 3000.0;           // 最小灵气值
    private double auraMax = 5000.0;           // 最大灵气值
    private double startingMagiculeMin = 2000.0; // 最小初始魔素值
    private double startingMagiculeMax = 3000.0; // 最大初始魔素值

    private float playerSize = 3.0f;           // 玩家大小

    public AranreivuRace() {
        super(Difficulty.EASY);
    }

    @Override
    public double getBaseHealth() {
        return baseHealth;
    }

    @Override
    public float getPlayerSize() {
        return playerSize;
    }

    @Override
    public double getBaseAttackDamage() {
        return baseAttackDamage;
    }

    @Override
    public double getBaseAttackSpeed() {
        return baseAttackSpeed;
    }

    @Override
    public double getKnockbackResistance() {
        return knockbackResistance;
    }

    @Override
    public double getJumpHeight() {
        return JumpPowerHelper.defaultPlayer(jumpHeight);
    }

    @Override
    public double getMovementSpeed() {
        return movementSpeed;
    }

    @Override
    public double getSprintSpeed() {
        return sprintSpeed;
    }

    @Override
    public Pair<Double, Double> getBaseAuraRange() {
        return Pair.of(auraMin, auraMax);
    }

    @Override
    public Pair<Double, Double> getBaseMagiculeRange() {
        return Pair.of(startingMagiculeMin, startingMagiculeMax);
    }

    @Override
    public List<TensuraSkill> getIntrinsicSkills(Player player) {
        List<TensuraSkill> list = new ArrayList<>();
        // 不添加技能，按照用户要求
        return list;
    }

    @Override
    public boolean isMajin() {
        return false;
    }

    @Override
    public boolean isSpiritual() {
        return false;
    }

    @Override
    public boolean isDivine() {
        return true;
    }

    @Override
    public void raceAbility(Player player) {
        if (!player.isSpectator() && !player.isCreative()) {
            if (player.getAbilities().mayfly) {
                // 关闭飞行
                player.getAbilities().mayfly = false;
                player.getAbilities().flying = false;
                player.onUpdateAbilities();
            } else {
                // 开启飞行
                player.getAbilities().mayfly = true;
                player.getAbilities().flying = true;
                player.onUpdateAbilities();
            }
        }
    }

    @Override
    public double getEvolutionPercentage(Player player) {
        return TensuraPlayerCapability.getBaseEP(player) * 100.0D / 800000.0D;
    }

    @Override
    public List<Component> getRequirementsForRendering(Player player) {
        List<Component> list = new ArrayList<>();
        list.add(Component.translatable("tensura.evolution_menu.ep_requirement"));
        return list;
    }

    @Override
    public List<Race> getNextEvolutions(Player player) {
        List<Race> list = new ArrayList<>();
        // 作为最终形态，没有更高级进化
        return list;
    }

    @Nullable
    @Override
    public Race getDefaultEvolution(Player player) {
        return null; // 没有默认进化
    }

    @Nullable
    @Override
    public Race getAwakeningEvolution(Player player) {
        return null; // 没有觉醒进化
    }

    @Nullable
    @Override
    public Race getHarvestFestivalEvolution(Player player) {
        return null; // 没有丰收节进化
    }
} 