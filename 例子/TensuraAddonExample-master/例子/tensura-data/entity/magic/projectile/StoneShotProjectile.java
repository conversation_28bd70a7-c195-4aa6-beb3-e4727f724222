package com.github.manasmods.tensura.entity.magic.projectile;

import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.entity.magic.TensuraProjectile;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import java.util.Optional;
import net.minecraft.core.particles.BlockParticleOption;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.EquipmentSlot.Type;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.phys.Vec3;

public class StoneShotProjectile extends TensuraProjectile {
   public StoneShotProjectile(EntityType<? extends StoneShotProjectile> entityType, Level level) {
      super(entityType, level);
   }

   public StoneShotProjectile(Level levelIn, LivingEntity shooter) {
      super((EntityType)TensuraEntityTypes.STONE_SHOT.get(), levelIn);
      this.m_5602_(shooter);
   }

   public String getMagic() {
      return "tensura.earth_attack";
   }

   public boolean shouldDiscardInWater() {
      return false;
   }

   public ResourceLocation[] getTextureLocation() {
      return new ResourceLocation[]{new ResourceLocation("tensura", "textures/entity/projectiles/stone_shot.png")};
   }

   public void setPosAndShoot(LivingEntity entity) {
      this.m_146884_(entity.m_20182_().m_82520_(0.0D, (double)entity.m_20192_(), 0.0D));
      this.shootFromRot(entity.m_20154_());
   }

   public static void breakTargetArmor(LivingEntity target, int amount) {
      EquipmentSlot[] var2 = EquipmentSlot.values();
      int var3 = var2.length;

      for(int var4 = 0; var4 < var3; ++var4) {
         EquipmentSlot slot = var2[var4];
         if (slot.m_20743_().equals(Type.ARMOR)) {
            ItemStack slotStack = target.m_6844_(slot);
            slotStack.m_41622_(amount, target, (player) -> {
               player.m_21166_(slot);
            });
         }
      }

   }

   public Optional<SoundEvent> hitSound() {
      return Optional.of(SoundEvents.f_12442_);
   }

   public void hitParticles(double x, double y, double z) {
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, new BlockParticleOption(ParticleTypes.f_123794_, Blocks.f_50069_.m_49966_()), x, y, z, 10, 0.08D, 0.08D, 0.08D, 0.1D, false);
   }

   public void flyingParticles() {
      Vec3 vec3 = this.m_20182_().m_82546_(this.m_20184_().m_82490_(2.0D));
      this.f_19853_.m_7106_(ParticleTypes.f_123797_, vec3.f_82479_, vec3.f_82480_, vec3.f_82481_, 0.0D, 0.0D, 0.0D);
   }
}
