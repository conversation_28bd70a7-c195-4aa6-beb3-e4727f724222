package com.github.manasmods.tensura.block;

import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.core.BlockPos.MutableBlockPos;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.tags.BlockTags;
import net.minecraft.util.RandomSource;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.item.context.BlockPlaceContext;
import net.minecraft.world.level.BlockGetter;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.LeavesBlock;
import net.minecraft.world.level.block.SoundType;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.BlockBehaviour.Properties;
import net.minecraft.world.level.block.state.StateDefinition.Builder;
import net.minecraft.world.level.block.state.properties.BlockStateProperties;
import net.minecraft.world.level.block.state.properties.IntegerProperty;
import net.minecraft.world.level.block.state.properties.Property;
import net.minecraft.world.level.material.FluidState;
import net.minecraft.world.level.material.Fluids;
import net.minecraft.world.level.material.Material;

public class SimpleLeaves extends LeavesBlock {
   public static final IntegerProperty DISTANCE_15;

   public SimpleLeaves() {
      this(SoundType.f_56740_);
   }

   public SimpleLeaves(SoundType soundType) {
      super(Properties.m_60939_(Material.f_76274_).m_60978_(0.2F).m_60977_().m_60918_(soundType).m_60955_().m_60922_(SimpleLeaves::ocelotOrBird).m_60960_(SimpleLeaves::never).m_60971_(SimpleLeaves::never));
      this.m_49959_((BlockState)((BlockState)((BlockState)((BlockState)((BlockState)this.f_49792_.m_61090_()).m_61124_(f_54418_, 7)).m_61124_(DISTANCE_15, 15)).m_61124_(f_54419_, Boolean.FALSE)).m_61124_(f_221367_, Boolean.FALSE));
   }

   public boolean m_6724_(BlockState pState) {
      if (pState.m_60734_().equals(TensuraBlocks.PALM_LEAVES.get())) {
         return false;
      } else {
         return (Integer)pState.m_61143_(DISTANCE_15) == 15 && !(Boolean)pState.m_61143_(f_54419_);
      }
   }

   protected boolean m_221385_(BlockState state) {
      return !(Boolean)state.m_61143_(f_54419_) && (Integer)state.m_61143_(DISTANCE_15) == 15;
   }

   public void m_213897_(BlockState pState, ServerLevel pLevel, BlockPos pPos, RandomSource pRandom) {
      pLevel.m_7731_(pPos, m_54435_(pState, pLevel, pPos), 3);
   }

   public BlockState m_7417_(BlockState pState, Direction pFacing, BlockState pFacingState, LevelAccessor pLevel, BlockPos pCurrentPos, BlockPos pFacingPos) {
      if ((Boolean)pState.m_61143_(f_221367_)) {
         pLevel.m_186469_(pCurrentPos, Fluids.f_76193_, Fluids.f_76193_.m_6718_(pLevel));
      }

      int i = m_54463_(pFacingState) + 1;
      if (i != 1 || (Integer)pState.m_61143_(DISTANCE_15) != i) {
         pLevel.m_186460_(pCurrentPos, this, 1);
      }

      return pState;
   }

   private static BlockState m_54435_(BlockState pState, LevelAccessor pLevel, BlockPos pPos) {
      int i = 15;
      MutableBlockPos blockpos$mutableblockpos = new MutableBlockPos();
      Direction[] var5 = Direction.values();
      int var6 = var5.length;

      for(int var7 = 0; var7 < var6; ++var7) {
         Direction direction = var5[var7];
         blockpos$mutableblockpos.m_122159_(pPos, direction);
         i = Math.min(i, m_54463_(pLevel.m_8055_(blockpos$mutableblockpos)) + 1);
         if (i == 1) {
            break;
         }
      }

      return (BlockState)pState.m_61124_(DISTANCE_15, i);
   }

   private static int m_54463_(BlockState pNeighbor) {
      if (pNeighbor.m_204336_(BlockTags.f_13106_)) {
         return 0;
      } else {
         return pNeighbor.m_60734_() instanceof SimpleLeaves ? (Integer)pNeighbor.m_61143_(DISTANCE_15) : 15;
      }
   }

   protected void m_7926_(Builder<Block, BlockState> pBuilder) {
      pBuilder.m_61104_(new Property[]{f_54418_, DISTANCE_15, f_54419_, f_221367_});
   }

   public BlockState m_5573_(BlockPlaceContext pContext) {
      FluidState fluidstate = pContext.m_43725_().m_6425_(pContext.m_8083_());
      BlockState blockstate = (BlockState)((BlockState)((BlockState)this.m_49966_().m_61124_(f_54419_, Boolean.TRUE)).m_61124_(f_54418_, 7)).m_61124_(f_221367_, fluidstate.m_76152_() == Fluids.f_76193_);
      return m_54435_(blockstate, pContext.m_43725_(), pContext.m_8083_());
   }

   public static boolean ocelotOrBird(BlockState state, BlockGetter getter, BlockPos pos, EntityType<?> type) {
      return type == EntityType.f_20505_ || type == EntityType.f_20508_ || type == TensuraEntityTypes.ONE_EYED_OWL.get() || type == TensuraEntityTypes.DRAGON_PEACOCK.get();
   }

   public static boolean never(BlockState state, BlockGetter getter, BlockPos pos) {
      return false;
   }

   static {
      DISTANCE_15 = BlockStateProperties.f_61410_;
   }
}
