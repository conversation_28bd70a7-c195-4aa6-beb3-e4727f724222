package com.github.manasmods.tensura.block;

import java.util.Optional;
import javax.annotation.Nullable;
import net.minecraft.core.Direction.Axis;
import net.minecraft.world.item.context.UseOnContext;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.RotatedPillarBlock;
import net.minecraft.world.level.block.SoundType;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.BlockBehaviour.Properties;
import net.minecraft.world.level.material.Material;
import net.minecraft.world.level.material.MaterialColor;
import net.minecraftforge.common.ToolAction;
import net.minecraftforge.common.ToolActions;
import net.minecraftforge.registries.RegistryObject;

public class SimpleLog extends RotatedPillarBlock {
   private final Optional<RegistryObject<? extends Block>> strippedLog;

   public SimpleLog(MaterialColor topColor, MaterialColor sideColor) {
      this(topColor, sideColor, (RegistryObject)null);
   }

   public SimpleLog(MaterialColor topColor, MaterialColor sideColor, @Nullable RegistryObject<? extends Block> strippedLog) {
      super(Properties.m_60947_(Material.f_76320_, (blockState) -> {
         return blockState.m_61143_(RotatedPillarBlock.f_55923_) == Axis.Y ? topColor : sideColor;
      }).m_60978_(2.0F).m_60918_(SoundType.f_56736_));
      this.strippedLog = Optional.ofNullable(strippedLog);
   }

   @Nullable
   public BlockState getToolModifiedState(BlockState state, UseOnContext context, ToolAction toolAction, boolean simulate) {
      return toolAction.equals(ToolActions.AXE_STRIP) ? (BlockState)this.strippedLog.map(RegistryObject::get).map((block) -> {
         return (BlockState)block.m_49966_().m_61124_(f_55923_, (Axis)state.m_61143_(f_55923_));
      }).orElse(super.getToolModifiedState(state, context, toolAction, simulate)) : super.getToolModifiedState(state, context, toolAction, simulate);
   }
}
