package com.github.b4ndithelps.tennogamenolife.registry.entity;

import com.github.b4ndithelps.tennogamenolife.Tenreincarnation;
import com.github.b4ndithelps.tennogamenolife.entity.HurtDummyEntity;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.MobCategory;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;


public class TenreincarnationEntities {

    public static final DeferredRegister<EntityType<?>> ENTITY_TYPES = 
        DeferredRegister.create(ForgeRegistries.ENTITY_TYPES, Tenreincarnation.MODID);
    public static final RegistryObject<EntityType<HurtDummyEntity>> HURT_DUMMY = 
        ENTITY_TYPES.register("hurt_dummy", 
            () -> EntityType.Builder.<HurtDummyEntity>of(HurtDummyEntity::new, MobCategory.MISC)
                .sized(0.7F, 1.9F)
                .clientTrackingRange(8)
                .build(new ResourceLocation(Tenreincarnation.MODID, "hurt_dummy").toString())
        );
    public static void register(IEventBus eventBus) {
        ENTITY_TYPES.register(eventBus);
    }
} 