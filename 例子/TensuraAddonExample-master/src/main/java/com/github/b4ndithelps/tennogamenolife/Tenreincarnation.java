package com.github.b4ndithelps.tennogamenolife;

import com.github.b4ndithelps.tennogamenolife.command.JorCommand;
import com.github.b4ndithelps.tennogamenolife.config.TenreincarnationConfig;
import com.github.b4ndithelps.tennogamenolife.registry.block.TenreincarnationBlocks;
import com.github.b4ndithelps.tennogamenolife.registry.entity.TenreincarnationEntities;
import com.github.b4ndithelps.tennogamenolife.registry.item.Tenreincarnationitems;
import com.github.b4ndithelps.tennogamenolife.registry.race.Tenreincarnationraces;
import com.github.b4ndithelps.tennogamenolife.registry.skill.TenreincarnationSkill;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.race.Race;
import com.mojang.logging.LogUtils;
import net.minecraft.client.Minecraft;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.RegisterCommandsEvent;
import net.minecraftforge.event.entity.player.PlayerEvent;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.ModLoadingContext;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.config.ModConfig;
import net.minecraftforge.fml.event.lifecycle.FMLClientSetupEvent;
import net.minecraftforge.fml.event.lifecycle.FMLCommonSetupEvent;
import net.minecraftforge.event.server.ServerStartingEvent;
import net.minecraftforge.fml.javafmlmod.FMLJavaModLoadingContext;
import org.slf4j.Logger;
import com.github.b4ndithelps.tennogamenolife.registry.effect.ModEffects;
import com.github.b4ndithelps.tennogamenolife.entity.HurtDummyEntity;
import net.minecraftforge.event.entity.EntityAttributeCreationEvent;
import net.minecraftforge.event.level.BlockEvent;
import com.github.b4ndithelps.tennogamenolife.ability.skill.unique.ChainMiningSkill;

@Mod(Tenreincarnation.MODID)
public class Tenreincarnation {

    public static final String MODID = "tennogamenolife";
    private static final Logger LOGGER = LogUtils.getLogger();
    private final TenreincarnationAddon addon;

    public Tenreincarnation() {
        IEventBus modEventBus = FMLJavaModLoadingContext.get().getModEventBus();
        modEventBus.addListener(this::setup);

        // 注册配置
        ModLoadingContext.get().registerConfig(ModConfig.Type.COMMON, TenreincarnationConfig.COMMON_SPEC);

        // 注册技能、种族和物品
        register(modEventBus);
        
        // 注册事件监听器
        MinecraftForge.EVENT_BUS.register(this);
        
        // 创建并注册addon
        this.addon = new TenreincarnationAddon();
        modEventBus.addListener(this.addon::onCommonSetup);
        
        // 注册效果
        ModEffects.MOB_EFFECTS.register(modEventBus);
        
        
        LOGGER.info("TenNoGameNoLife 模组初始化完成！");
    }

    private void setup(final FMLCommonSetupEvent event) {
        LOGGER.info("TenNoGameNoLife 模组初始化中...");
        LOGGER.info("TenNoGameNoLife 模组初始化完成！");
    }

    @SubscribeEvent
    public void onServerStarting(ServerStartingEvent event) {
        // No need to initialize capabilities here as they are handled by the main mod
    }
    
    /**
     * 注册命令
     */
    @SubscribeEvent
    public void onRegisterCommands(RegisterCommandsEvent event) {
        LOGGER.info("注册命令: /aqzl");
        JorCommand.register(event.getDispatcher());
    }

    @SubscribeEvent
    public void onPlayerLoggedIn(PlayerEvent.PlayerLoggedInEvent event) {
        Player player = event.getEntity();
        if (player != null) {
            player.getCapability(TensuraPlayerCapability.CAPABILITY).ifPresent(cap -> {
                Race currentRace = cap.getRace();
                LOGGER.info("Player {} logged in with race: {}", 
                    player.getName().getString(), 
                    currentRace != null ? currentRace.getClass().getSimpleName() : "null");
            });
        }
    }

    @Mod.EventBusSubscriber(modid = MODID, bus = Mod.EventBusSubscriber.Bus.MOD, value = Dist.CLIENT)
    public static class ClientModEvents {

        @SubscribeEvent
        public static void onClientSetup(FMLClientSetupEvent event) {
            LOGGER.info("HELLO FROM CLIENT SETUP");
            LOGGER.info("MINECRAFT NAME >> {}", Minecraft.getInstance().getUser().getName());
        }
    }

    @Mod.EventBusSubscriber(modid = MODID, bus = Mod.EventBusSubscriber.Bus.MOD)
    public static class CommonModEvents {
        /**
         * 注册实体属性
         */
        @SubscribeEvent
        public static void onAttributeCreate(EntityAttributeCreationEvent event) {
            event.put(TenreincarnationEntities.HURT_DUMMY.get(), HurtDummyEntity.createAttributes().build());
        }
    }

    private void register(final IEventBus modEventBus) {
        TenreincarnationSkill.register(modEventBus);
        Tenreincarnationraces.register(modEventBus);
        Tenreincarnationitems.register(modEventBus);
        TenreincarnationBlocks.register(modEventBus);
        TenreincarnationEntities.register(modEventBus);
    }
}
