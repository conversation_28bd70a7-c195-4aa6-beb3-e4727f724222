package com.github.b4ndithelps.tennogamenolife.ability.skill.extra;

import com.github.b4ndithelps.tennogamenolife.Tenreincarnation;
import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.ability.skill.Skill.SkillType;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.ClipContext;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.EntityHitResult;
import net.minecraft.world.phys.HitResult;
import net.minecraft.world.phys.Vec3;

import java.util.List;
import java.util.Optional;
import java.util.function.Predicate;

/**
 * 通行管制技能
 * 限制敌人或对手的行动
 */
public class BarrierSkill extends Skill {


    @Override
    public ResourceLocation getSkillIcon() {
        return new ResourceLocation(Tenreincarnation.MODID, "textures/skill/extra/umweg.png");
    }

    // 技能参数配置
    private final double skillCastCost = 100.0;     // 技能消耗魔素值
    private final double learnCost = 2.0;         // 学习难度
    private final double skillRange = 15.0;       // 技能有效范围（15格）

    public BarrierSkill() {
        super(SkillType.EXTRA);
    }

    /**
     * 获取技能消耗的魔素值
     */
    @Override
    public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
        return skillCastCost;
    }

    /**
     * 获取学习难度
     */
    @Override
    public double learningCost() {
        return learnCost;
    }

    /**
     * 检查技能是否可以切换
     */
    @Override
    public boolean canBeToggled(ManasSkillInstance instance, LivingEntity entity) {
        return false; 
    }

    /**
     * 检查技能是否可以持续生效
     */
    @Override
    public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
        return false; 
    }

    /**
     * 技能按下时的效果
     */
    @Override
    public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
        if (entity instanceof Player player) {
            player.getLevel().playSound(null, player.getX(), player.getY(), player.getZ(), 
                SoundEvents.EVOKER_PREPARE_ATTACK, SoundSource.PLAYERS, 1.0F, 1.0F);

            if (SkillHelper.outOfMagicule(entity, this.magiculeCost(entity, instance))) {
                return;
            }
            

            LivingEntity target = findTargetByRaytrace(player, skillRange);
            
            if (target == null) {
                target = findNearestEntityInLine(player, skillRange);
            }
            
            if (target != null) {

                target.addEffect(new MobEffectInstance(MobEffects.MOVEMENT_SLOWDOWN, 200, 255, false, true, true));

                target.addEffect(new MobEffectInstance(MobEffects.DIG_SLOWDOWN, 200, 255, false, true, true));

                player.getLevel().playSound(null, target.getX(), target.getY(), target.getZ(), 
                    SoundEvents.ANVIL_LAND, SoundSource.PLAYERS, 1.0F, 0.5F);

                instance.setCoolDown(5);

                this.addMasteryPoint(instance, entity);
            }
        }
    }
    
    /**
     * 使用光线追踪查找玩家指向的实体
     */
    private LivingEntity findTargetByRaytrace(Player player, double maxDistance) {
        Level level = player.level;

        Vec3 startPos = player.getEyePosition(1.0F);

        Vec3 lookVec = player.getViewVector(1.0F);

        Vec3 endPos = startPos.add(lookVec.scale(maxDistance));

        AABB boundingBox = player.getBoundingBox().expandTowards(lookVec.scale(maxDistance)).inflate(1.0D);

        List<Entity> possibleEntities = level.getEntities(player, boundingBox, 
                (entity) -> entity instanceof LivingEntity && !entity.isSpectator() && entity.isPickable());

        Entity nearestEntity = null;
        double nearestDistance = maxDistance;
        Vec3 nearestIntersection = null;
        
        for (Entity entity : possibleEntities) {

            AABB entityBox = entity.getBoundingBox().inflate(0.3D);

            Optional<Vec3> hitPosition = entityBox.clip(startPos, endPos);
            
            if (hitPosition.isPresent()) {

                double distance = startPos.distanceTo(hitPosition.get());

                if (distance < nearestDistance) {
                    nearestEntity = entity;
                    nearestDistance = distance;
                    nearestIntersection = hitPosition.get();
                }
            }
        }

        if (nearestEntity instanceof LivingEntity living) {
            return living;
        }
        
        return null;
    }
    
    /**
     * 寻找玩家视线方向上最近的实体
     */
    private LivingEntity findNearestEntityInLine(Player player, double maxDistance) {
        Level level = player.level;

        Vec3 eyePos = player.getEyePosition(1.0F);

        Vec3 viewVector = player.getViewVector(1.0F);
        Vec3 targetPos = eyePos.add(viewVector.x * maxDistance, viewVector.y * maxDistance, viewVector.z * maxDistance);

        double boxSize = 2.0;
        AABB detectionBox = new AABB(
            eyePos.x - boxSize, eyePos.y - boxSize, eyePos.z - boxSize,
            targetPos.x + boxSize, targetPos.y + boxSize, targetPos.z + boxSize
        );

        List<Entity> entities = level.getEntities(player, detectionBox, 
                (entity) -> entity instanceof LivingEntity && entity != player);

        Entity closestEntity = null;
        double closestDistance = maxDistance;
        double smallestDeviation = Double.MAX_VALUE;
        
        for (Entity entity : entities) {

            double distance = player.distanceTo(entity);
            
            if (distance <= maxDistance) {

                Vec3 entityPos = entity.position().add(0, entity.getEyeHeight() / 2, 0);
                Vec3 playerToEntity = entityPos.subtract(eyePos);
                double length = playerToEntity.length();

                Vec3 projection = eyePos.add(viewVector.scale(length * viewVector.dot(playerToEntity) / viewVector.lengthSqr()));
                double deviation = projection.distanceTo(entityPos);

                if (deviation < smallestDeviation && deviation < 2.0) {
                    closestEntity = entity;
                    closestDistance = distance;
                    smallestDeviation = deviation;
                }
            }
        }
        
        if (closestEntity instanceof LivingEntity living) {
            return living;
        }
        
        return null;
    }

    /**
     * 技能持续按住时的效果
     */
    @Override
    public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
        return false;
    }

    /**
     * 技能释放时的效果
     */
    @Override
    public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
    }

    /**
     * 技能持续生效时的效果
     */
    @Override
    public void onTick(ManasSkillInstance instance, LivingEntity entity) {
    }

    /**
     * 技能开启时的效果
     */
    @Override
    public void onToggleOn(ManasSkillInstance instance, LivingEntity entity) {
    }

    /**
     * 技能关闭时的效果
     */
    @Override
    public void onToggleOff(ManasSkillInstance instance, LivingEntity entity) {
    }
} 