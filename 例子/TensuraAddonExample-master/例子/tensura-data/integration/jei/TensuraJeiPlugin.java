package com.github.manasmods.tensura.integration.jei;

import com.github.manasmods.tensura.client.screen.KilnScreen;
import com.github.manasmods.tensura.data.recipe.GreatSageRefiningRecipe;
import com.github.manasmods.tensura.data.recipe.KilnMeltingRecipe;
import com.github.manasmods.tensura.data.recipe.KilnMixingRecipe;
import com.github.manasmods.tensura.data.recipe.SmithingBenchRecipe;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.github.manasmods.tensura.registry.items.TensuraConsumableItems;
import com.github.manasmods.tensura.registry.recipe.TensuraRecipeTypes;
import java.util.Objects;
import mezz.jei.api.IModPlugin;
import mezz.jei.api.JeiPlugin;
import mezz.jei.api.recipe.RecipeType;
import mezz.jei.api.recipe.category.IRecipeCategory;
import mezz.jei.api.registration.IGuiHandlerRegistration;
import mezz.jei.api.registration.IRecipeCatalystRegistration;
import mezz.jei.api.registration.IRecipeCategoryRegistration;
import mezz.jei.api.registration.IRecipeRegistration;
import net.minecraft.client.Minecraft;
import net.minecraft.client.multiplayer.ClientLevel;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.crafting.RecipeManager;

@JeiPlugin
public class TensuraJeiPlugin implements IModPlugin {
   static final RecipeType<KilnMeltingRecipe> KILN_MELTING_RECIPE;
   static final RecipeType<KilnMixingRecipe> KILN_MIXING_RECIPE;
   static final RecipeType<SmithingBenchRecipe> SMITHING_RECIPE;
   static final RecipeType<GreatSageRefiningRecipe> REFINING_RECIPE;

   public ResourceLocation getPluginUid() {
      return new ResourceLocation("tensura", "jei_plugin");
   }

   public void registerCategories(IRecipeCategoryRegistration registration) {
      registration.addRecipeCategories(new IRecipeCategory[]{new KilnMeltingRecipeCategory(registration.getJeiHelpers().getGuiHelper())});
      registration.addRecipeCategories(new IRecipeCategory[]{new KilnMixingRecipeCategory(registration.getJeiHelpers().getGuiHelper())});
      registration.addRecipeCategories(new IRecipeCategory[]{new SmithingRecipeCategory(registration.getJeiHelpers().getGuiHelper())});
      registration.addRecipeCategories(new IRecipeCategory[]{new GreatSageRefiningRecipeCategory(registration.getJeiHelpers().getGuiHelper())});
   }

   public void registerRecipes(IRecipeRegistration registration) {
      RecipeManager rm = ((ClientLevel)Objects.requireNonNull(Minecraft.m_91087_().f_91073_)).m_7465_();
      registration.addRecipes(KILN_MELTING_RECIPE, rm.m_44013_((net.minecraft.world.item.crafting.RecipeType)TensuraRecipeTypes.KILN_MELTING.get()));
      registration.addRecipes(KILN_MIXING_RECIPE, rm.m_44013_((net.minecraft.world.item.crafting.RecipeType)TensuraRecipeTypes.KILN_MIXING.get()));
      registration.addRecipes(SMITHING_RECIPE, rm.m_44013_((net.minecraft.world.item.crafting.RecipeType)TensuraRecipeTypes.SMITHING.get()));
      registration.addRecipes(REFINING_RECIPE, rm.m_44013_((net.minecraft.world.item.crafting.RecipeType)TensuraRecipeTypes.REFINING.get()));
   }

   public void registerRecipeCatalysts(IRecipeCatalystRegistration registration) {
      registration.addRecipeCatalyst(((Item)TensuraBlocks.Items.KILN.get()).m_7968_(), new RecipeType[]{KILN_MELTING_RECIPE});
      registration.addRecipeCatalyst(((Item)TensuraBlocks.Items.KILN.get()).m_7968_(), new RecipeType[]{KILN_MIXING_RECIPE});
      registration.addRecipeCatalyst(((Item)TensuraBlocks.Items.SMITHING_BENCH.get()).m_7968_(), new RecipeType[]{SMITHING_RECIPE});
      registration.addRecipeCatalyst(((Item)TensuraConsumableItems.FULL_POTION.get()).m_7968_(), new RecipeType[]{REFINING_RECIPE});
   }

   public void registerGuiHandlers(IGuiHandlerRegistration registration) {
      registration.addRecipeClickArea(KilnScreen.class, 203, 77, 17, 15, new RecipeType[]{KILN_MELTING_RECIPE});
      registration.addRecipeClickArea(KilnScreen.class, 79, 4, 18, 14, new RecipeType[]{KILN_MIXING_RECIPE});
   }

   static {
      KILN_MELTING_RECIPE = new RecipeType(KilnMeltingRecipeCategory.UID, KilnMeltingRecipe.class);
      KILN_MIXING_RECIPE = new RecipeType(KilnMixingRecipeCategory.UID, KilnMixingRecipe.class);
      SMITHING_RECIPE = new RecipeType(SmithingRecipeCategory.UID, SmithingBenchRecipe.class);
      REFINING_RECIPE = new RecipeType(GreatSageRefiningRecipeCategory.UID, GreatSageRefiningRecipe.class);
   }
}
