package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.effect.template.MobEffectHelper;
import com.github.manasmods.tensura.event.ForcedTeleportationEvent;
import com.github.manasmods.tensura.menu.SpatialMenu;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.github.manasmods.tensura.registry.dimensions.TensuraDimensions;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import net.minecraft.ChatFormatting;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.SimpleMenuProvider;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.network.NetworkHooks;
import org.jetbrains.annotations.Nullable;

public class SpatialMotionSkill extends Skill {
   public SpatialMotionSkill() {
      super(Skill.SkillType.EXTRA);
   }

   public double learningCost() {
      return 500.0D;
   }

   public int modes() {
      return 2;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      return instance.getMode() == 1 ? 2 : 1;
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.spatial_motion.blink");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.spatial_motion.warp");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 0.0D;
   }

   public void onSkillMastered(ManasSkillInstance instance, LivingEntity entity) {
      SkillStorage storage = SkillAPI.getSkillsFrom(entity);
      ManasSkill skill = (ManasSkill)ExtraSkills.SPATIAL_MANIPULATION.get();
      ManasSkillInstance manipulation = new TensuraSkillInstance(skill);
      manipulation.setMastery(-100);
      if (storage.learnSkill(manipulation) && entity instanceof Player) {
         Player player = (Player)entity;
         player.m_5661_(Component.m_237110_("tensura.skill.learn_available", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_GREEN)), false);
      }

   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (MobEffectHelper.noTeleportation(entity)) {
         if (entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237115_("tensura.skill.spatial_blockade").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
         }

      } else {
         Level level = entity.m_9236_();
         switch(instance.getMode()) {
         case 1:
            BlockHitResult result = SkillHelper.getPlayerPOVHitResult(level, entity, Fluid.NONE, this.isMastered(instance, entity) ? 50.0D : 30.0D);
            BlockPos pos = result.m_82425_().m_121945_(result.m_82434_());
            if (level.m_8055_(pos).m_60713_((Block)TensuraBlocks.LABYRINTH_BARRIER_BLOCK.get())) {
               level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12317_, SoundSource.PLAYERS, 1.0F, 1.0F);
               return;
            }

            if (SkillHelper.outOfMagicule(entity, 10.0D * Math.sqrt(entity.m_20275_((double)pos.m_123341_(), (double)pos.m_123342_(), (double)pos.m_123343_())))) {
               return;
            }

            warp(entity, (double)pos.m_123341_(), (double)pos.m_123342_(), (double)pos.m_123343_());
            this.addMasteryPoint(instance, entity);
            instance.setCoolDown(instance.isMastered(entity) ? 2 : 5);
            break;
         case 2:
            if (entity instanceof ServerPlayer) {
               ServerPlayer serverPlayer = (ServerPlayer)entity;
               if (level.m_46472_() == TensuraDimensions.LABYRINTH) {
                  serverPlayer.m_6330_(SoundEvents.f_12317_, SoundSource.PLAYERS, 1.0F, 1.0F);
                  serverPlayer.m_5661_(Component.m_237115_("tensura.ability.activation_failed").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
               } else {
                  NetworkHooks.openScreen(serverPlayer, new SimpleMenuProvider(SpatialMenu::new, Component.m_237119_()), (buf) -> {
                     buf.writeBoolean(false);
                  });
                  serverPlayer.m_6330_(SoundEvents.f_11889_, SoundSource.PLAYERS, 1.0F, 1.0F);
               }
            }

            entity.m_21195_((MobEffect)TensuraMobEffects.WARPING.get());
            instance.setCoolDown(instance.isMastered(entity) ? 10 : 20);
         }

      }
   }

   public static void warp(LivingEntity living, double x, double y, double z) {
      warp(living, living, x, y, z);
   }

   public static void warp(LivingEntity living, @Nullable Entity warper, double x, double y, double z) {
      ForcedTeleportationEvent event = new ForcedTeleportationEvent(living, warper, x, y, z);
      if (!MinecraftForge.EVENT_BUS.post(event)) {
         living.m_9236_().m_6263_((Player)null, living.m_20185_(), living.m_20186_(), living.m_20189_(), SoundEvents.f_11757_, SoundSource.PLAYERS, 1.0F, 1.0F);
         TensuraParticleHelper.addServerParticlesAroundSelf(living, ParticleTypes.f_123760_, 1.0D);
         living.m_183634_();
         if (living.m_9236_().m_6857_().m_61937_(new BlockPos(x, y, z))) {
            living.m_19877_();
            living.m_20324_(x, y, z);
         } else if (living instanceof Player) {
            Player player = (Player)living;
            player.m_5661_(Component.m_237115_("tensura.skill.teleport.out_border").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
         }

         TensuraParticleHelper.addServerParticlesAroundSelf(living, ParticleTypes.f_123760_, 1.0D);
         living.m_9236_().m_6263_((Player)null, living.m_20185_(), living.m_20186_(), living.m_20189_(), SoundEvents.f_11757_, SoundSource.PLAYERS, 1.0F, 1.0F);
      }
   }
}
