package com.github.b4ndithelps.tennogamenolife.registry.block;

import com.github.b4ndithelps.tennogamenolife.Tenreincarnation;
import com.github.b4ndithelps.tennogamenolife.block.ChessBlock;
import com.github.b4ndithelps.tennogamenolife.registry.item.Tenreincarnationitems;

import net.minecraft.world.item.BlockItem;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.Rarity;
import net.minecraft.world.level.block.Block;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;

import java.util.function.Supplier;

public class TenreincarnationBlocks {
    public static final DeferredRegister<Block> BLOCKS = DeferredRegister.create(ForgeRegistries.BLOCKS, Tenreincarnation.MODID);

    /**
     * 注册国际象棋方块
     */
    public static final RegistryObject<Block> CHESS_BLOCK = registerBlock("chess_block",
            ChessBlock::new, new Item.Properties().rarity(Rarity.RARE));

    /**
     * @param name
     * @param supplier
     * @param properties
     * @return
     * @param <T>
     */
    private static <T extends Block> RegistryObject<T> registerBlock(String name, Supplier<T> supplier, Item.Properties properties) {
        RegistryObject<T> block = BLOCKS.register(name, supplier);
        registerBlockItem(name, block, properties);
        return block;
    }

    /**
     * @param name
     * @param block
     * @param properties
     * @return
     * @param <T>
     */
    private static <T extends Block> RegistryObject<Item> registerBlockItem(String name, RegistryObject<T> block, Item.Properties properties) {
        return Tenreincarnationitems.ITEMS.register(name, () -> new BlockItem(block.get(), properties));
    }

    /**
     * @param modEventBus
     */
    public static void register(IEventBus modEventBus) {
        BLOCKS.register(modEventBus);
    }
} 