package com.github.manasmods.tensura.enchantment;

import com.github.manasmods.tensura.entity.human.CloneEntity;
import com.github.manasmods.tensura.item.TensuraArmourMaterials;
import com.github.manasmods.tensura.item.TensuraToolTiers;
import com.github.manasmods.tensura.registry.enchantment.TensuraEnchantments;
import java.util.Objects;
import javax.annotation.Nullable;
import net.minecraft.ChatFormatting;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ArmorItem;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.TieredItem;
import net.minecraft.world.item.enchantment.Enchantment;
import net.minecraft.world.item.enchantment.EnchantmentCategory;
import net.minecraft.world.item.enchantment.Enchantment.Rarity;

public class TsukumogamiEnchantment extends EngravingEnchantment implements IInherentEngrave {
   public TsukumogamiEnchantment() {
      super(Rarity.VERY_RARE, EnchantmentCategory.BREAKABLE, EquipmentSlot.values());
   }

   public ChatFormatting getNameFormatting() {
      return ChatFormatting.RED;
   }

   public static void applyTsukumogami(ItemStack toStack, LivingEntity entity) {
      boolean var10000;
      Item var4;
      label38: {
         var4 = toStack.m_41720_();
         if (var4 instanceof TieredItem) {
            TieredItem item = (TieredItem)var4;
            if (item.m_43314_().equals(TensuraToolTiers.HIHIIROKANE)) {
               var10000 = true;
               break label38;
            }
         }

         var10000 = false;
      }

      boolean canApply;
      label45: {
         canApply = var10000;
         if (!canApply) {
            label42: {
               var4 = toStack.m_41720_();
               if (var4 instanceof ArmorItem) {
                  ArmorItem item = (ArmorItem)var4;
                  if (item.m_40401_().equals(TensuraArmourMaterials.HIHIIROKANE)) {
                     break label42;
                  }
               }

               var10000 = false;
               break label45;
            }
         }

         var10000 = true;
      }

      canApply = var10000;
      if (canApply) {
         if (toStack.getEnchantmentLevel((Enchantment)TensuraEnchantments.TSUKUMOGAMI.get()) < 1) {
            toStack.m_41663_((Enchantment)TensuraEnchantments.TSUKUMOGAMI.get(), 1);
            if (entity instanceof Player) {
               Player player = (Player)entity;
               CompoundTag var7 = toStack.m_41784_();
               var7.m_128362_("OwnerUUID", player.m_20148_());
            }
         }
      }
   }

   public static void updateOwnerHolding(ItemStack stack, @Nullable LivingEntity entity) {
      CompoundTag tag = stack.m_41783_();
      if (tag == null || !tag.m_128403_("OwnerUUID")) {
         if (stack.getEnchantmentLevel((Enchantment)TensuraEnchantments.TSUKUMOGAMI.get()) < 1) {
            return;
         }

         if (entity == null) {
            return;
         }

         CompoundTag newTag = stack.m_41784_();
         newTag.m_128362_("OwnerUUID", entity.m_20148_());
      }

      if (entity == null) {
         tag.m_128379_("Activated", false);
      } else if (tag != null) {
         boolean correctOwner = Objects.equals(tag.m_128342_("OwnerUUID"), entity.m_20148_()) || entity instanceof CloneEntity;
         if (tag.m_128471_("Activated") != correctOwner) {
            entity.m_5496_(SoundEvents.f_11739_, 0.3F, 1.0F);
         }

         tag.m_128379_("Activated", correctOwner);
      }

   }
}
