package com.github.manasmods.tensura.block;

import com.github.manasmods.tensura.block.state.properties.SmithingBenchPart;
import com.github.manasmods.tensura.block.state.properties.TensuraBlockStateProperties;
import com.github.manasmods.tensura.menu.SmithingBenchMenu;
import javax.annotation.Nullable;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.SimpleMenuProvider;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.inventory.ContainerLevelAccess;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.context.BlockPlaceContext;
import net.minecraft.world.level.BlockGetter;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.Mirror;
import net.minecraft.world.level.block.Rotation;
import net.minecraft.world.level.block.SimpleWaterloggedBlock;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.StateDefinition.Builder;
import net.minecraft.world.level.block.state.properties.BlockStateProperties;
import net.minecraft.world.level.block.state.properties.BooleanProperty;
import net.minecraft.world.level.block.state.properties.DirectionProperty;
import net.minecraft.world.level.block.state.properties.EnumProperty;
import net.minecraft.world.level.block.state.properties.Property;
import net.minecraft.world.level.material.FluidState;
import net.minecraft.world.level.material.Fluids;
import net.minecraft.world.level.material.Material;
import net.minecraft.world.level.material.PushReaction;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.shapes.CollisionContext;
import net.minecraft.world.phys.shapes.Shapes;
import net.minecraft.world.phys.shapes.VoxelShape;
import net.minecraftforge.network.NetworkHooks;

public class SmithingBenchBlock extends SimpleBlock implements SimpleWaterloggedBlock {
   public static final DirectionProperty FACING;
   public static final BooleanProperty WATERLOGGED;
   public static final EnumProperty<SmithingBenchPart> PART;
   public static final MutableComponent TITLE;

   public SmithingBenchBlock(Material material, SimpleBlock.BlockProperties properties) {
      super(material, properties);
      this.m_49959_((BlockState)((BlockState)((BlockState)((BlockState)this.f_49792_.m_61090_()).m_61124_(FACING, Direction.NORTH)).m_61124_(PART, SmithingBenchPart.BENCH)).m_61124_(WATERLOGGED, Boolean.FALSE));
   }

   public BlockState m_7417_(BlockState pState, Direction pDirection, BlockState pNeighborState, LevelAccessor pLevel, BlockPos pCurrentPos, BlockPos pNeighborPos) {
      if ((Boolean)pState.m_61143_(WATERLOGGED)) {
         pLevel.m_186469_(pCurrentPos, Fluids.f_76193_, Fluids.f_76193_.m_6718_(pLevel));
      }

      return super.m_7417_(pState, pDirection, pNeighborState, pLevel, pCurrentPos, pNeighborPos);
   }

   public void m_6402_(Level pLevel, BlockPos pPos, BlockState pState, @Nullable LivingEntity pPlacer, ItemStack pStack) {
      super.m_6402_(pLevel, pPos, pState, pPlacer, pStack);
      if (!pLevel.m_5776_()) {
         BlockPos blockpos = this.getOtherPartPosition(pPos, (Direction)pState.m_61143_(FACING), (SmithingBenchPart)pState.m_61143_(PART));
         pLevel.m_7731_(blockpos, (BlockState)((BlockState)pState.m_61124_(PART, SmithingBenchPart.ANVIL)).m_61124_(WATERLOGGED, this.isWaterAtPosition(pLevel, blockpos)), 3);
         pLevel.m_6289_(pPos, Blocks.f_50016_);
         pState.m_60701_(pLevel, pPos, 3);
      }
   }

   public VoxelShape m_5940_(BlockState pState, BlockGetter pLevel, BlockPos pPos, CollisionContext pContext) {
      SmithingBenchPart part = (SmithingBenchPart)pState.m_61143_(PART);
      if (part == SmithingBenchPart.ANVIL) {
         Direction direction = getConnectedDirection(pState).m_122424_();
         VoxelShape var10000;
         switch(direction) {
         case NORTH:
         case SOUTH:
            var10000 = Shapes.m_83124_(m_49796_(1.0D, 0.0D, 1.0D, 15.0D, 2.0D, 15.0D), new VoxelShape[]{m_49796_(3.0D, 2.0D, 3.0D, 13.0D, 5.0D, 13.0D), m_49796_(4.0D, 5.0D, 4.0D, 12.0D, 6.0D, 12.0D), m_49796_(5.0D, 6.0D, 5.0D, 11.0D, 10.0D, 11.0D), m_49796_(1.0D, 10.0D, 3.0D, 15.0D, 16.0D, 13.0D)});
            break;
         default:
            var10000 = Shapes.m_83124_(m_49796_(1.0D, 0.0D, 1.0D, 15.0D, 2.0D, 15.0D), new VoxelShape[]{m_49796_(3.0D, 2.0D, 3.0D, 13.0D, 5.0D, 13.0D), m_49796_(4.0D, 5.0D, 4.0D, 12.0D, 6.0D, 12.0D), m_49796_(5.0D, 6.0D, 5.0D, 11.0D, 10.0D, 11.0D), m_49796_(3.0D, 10.0D, 1.0D, 13.0D, 16.0D, 15.0D)});
         }

         return var10000;
      } else {
         return Block.m_49796_(0.0D, 0.0D, 0.0D, 16.0D, 16.0D, 16.0D);
      }
   }

   public static Direction getConnectedDirection(BlockState pState) {
      Direction direction = (Direction)pState.m_61143_(FACING);
      return pState.m_61143_(PART) == SmithingBenchPart.BENCH ? direction.m_122424_() : direction;
   }

   @Nullable
   public BlockState m_5573_(BlockPlaceContext pContext) {
      BlockPos blockpos = pContext.m_8083_();
      BlockPos other = blockpos.m_121945_(pContext.m_8125_().m_122428_());
      Level level = pContext.m_43725_();
      return level.m_6857_().m_61937_(other) && level.m_8055_(other).m_60629_(pContext) ? (BlockState)((BlockState)this.m_49966_().m_61124_(FACING, pContext.m_8125_().m_122424_())).m_61124_(WATERLOGGED, this.isWaterAtPosition(level, blockpos)) : null;
   }

   public BlockState m_6843_(BlockState pState, Rotation pRotation) {
      return (BlockState)pState.m_61124_(FACING, pRotation.m_55954_((Direction)pState.m_61143_(FACING)));
   }

   public BlockState m_6943_(BlockState pState, Mirror pMirror) {
      return pState.m_60717_(pMirror.m_54846_((Direction)pState.m_61143_(FACING)));
   }

   protected void m_7926_(Builder<Block, BlockState> pBuilder) {
      pBuilder.m_61104_(new Property[]{FACING, PART, WATERLOGGED});
   }

   public void m_5707_(Level pLevel, BlockPos pPos, BlockState pState, Player pPlayer) {
      super.m_5707_(pLevel, pPos, pState, pPlayer);
      if (!pLevel.m_5776_()) {
         Direction direction = (Direction)pState.m_61143_(FACING);
         BlockPos blockpos = this.getOtherPartPosition(pPos, direction, (SmithingBenchPart)pState.m_61143_(PART));
         pLevel.m_46597_(blockpos, Blocks.f_50016_.m_49966_());
      }

   }

   private BlockPos getOtherPartPosition(BlockPos sourcePos, Direction direction, SmithingBenchPart part) {
      return part == SmithingBenchPart.BENCH ? sourcePos.m_121945_(direction.m_122427_()) : sourcePos.m_121945_(direction.m_122428_());
   }

   private boolean isWaterAtPosition(Level level, BlockPos blockPos) {
      return level.m_6425_(blockPos).m_192917_(Fluids.f_76193_);
   }

   public PushReaction m_5537_(BlockState pState) {
      return PushReaction.BLOCK;
   }

   public FluidState m_5888_(BlockState pState) {
      return (Boolean)pState.m_61143_(WATERLOGGED) ? Fluids.f_76193_.m_76068_(false) : super.m_5888_(pState);
   }

   public InteractionResult m_6227_(BlockState pState, Level pLevel, BlockPos pPos, Player pPlayer, InteractionHand pHand, BlockHitResult pHit) {
      if (!pLevel.m_5776_()) {
         NetworkHooks.openScreen((ServerPlayer)pPlayer, new SimpleMenuProvider((pContainerId, pPlayerInventory, pPlayer1) -> {
            return new SmithingBenchMenu(pContainerId, pPlayerInventory, ContainerLevelAccess.m_39289_(pLevel, pPos));
         }, TITLE));
      }

      return InteractionResult.m_19078_(pLevel.m_5776_());
   }

   static {
      FACING = BlockStateProperties.f_61374_;
      WATERLOGGED = BlockStateProperties.f_61362_;
      PART = TensuraBlockStateProperties.SMITHING_BENCH_PART;
      TITLE = Component.m_237115_("block.tensura.smithing_bench");
   }
}
