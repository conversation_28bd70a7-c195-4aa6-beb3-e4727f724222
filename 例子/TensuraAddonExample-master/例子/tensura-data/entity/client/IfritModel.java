package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.IfritCloneEntity;
import com.github.manasmods.tensura.entity.IfritEntity;
import com.github.manasmods.tensura.entity.template.HumanoidNPCEntity;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.processor.IBone;
import software.bernie.geckolib3.model.AnimatedGeoModel;
import software.bernie.geckolib3.model.provider.data.EntityModelData;

public class IfritModel<T extends HumanoidNPCEntity & IAnimatable> extends AnimatedGeoModel<T> {
   public ResourceLocation getModelResource(T object) {
      return new ResourceLocation("tensura", "geo/ifrit.geo.json");
   }

   public ResourceLocation getTextureResource(T instance) {
      return new ResourceLocation("tensura", "textures/entity/ifrit/ifrit.png");
   }

   public ResourceLocation getAnimationResource(T ifrit) {
      return new ResourceLocation("tensura", "animations/ifrit.animation.json");
   }

   public void setCustomAnimations(T ifrit, int instanceId, AnimationEvent customPredicate) {
      super.setCustomAnimations(ifrit, instanceId, customPredicate);
      if (this.shouldStand(ifrit)) {
         EntityModelData extraData = (EntityModelData)customPredicate.getExtraDataOfType(EntityModelData.class).get(0);
         IBone head = this.getAnimationProcessor().getBone("Head");
         if (head != null) {
            head.setRotationX(extraData.headPitch * 3.1415927F / 180.0F);
            head.setRotationY(extraData.netHeadYaw * 3.1415927F / 180.0F);
         }
      }

   }

   private boolean shouldStand(T entity) {
      if (entity instanceof IfritEntity) {
         IfritEntity ifrit = (IfritEntity)entity;
         return ifrit.shouldStand();
      } else {
         boolean var10000;
         if (entity instanceof IfritCloneEntity) {
            IfritCloneEntity clone = (IfritCloneEntity)entity;
            if (clone.shouldStand()) {
               var10000 = true;
               return var10000;
            }
         }

         var10000 = false;
         return var10000;
      }
   }
}
