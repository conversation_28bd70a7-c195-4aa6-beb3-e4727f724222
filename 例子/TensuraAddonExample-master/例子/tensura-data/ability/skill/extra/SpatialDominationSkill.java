package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.entity.human.HinataSakaguchiEntity;
import com.github.manasmods.tensura.entity.magic.beam.BeamProjectile;
import com.github.manasmods.tensura.entity.magic.beam.SpatialRayProjectile;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import java.util.UUID;
import net.minecraft.ChatFormatting;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.event.entity.living.LivingDamageEvent;
import net.minecraftforge.event.entity.living.LivingHurtEvent;

public class SpatialDominationSkill extends Skill {
   private static final String FAULT = "9e5add62-dbfa-4130-ab0b-6612a8f4c4aa";

   public SpatialDominationSkill() {
      super(Skill.SkillType.EXTRA);
      this.addHeldAttributeModifier(Attributes.f_22279_, "9e5add62-dbfa-4130-ab0b-6612a8f4c4aa", -0.5D, Operation.MULTIPLY_TOTAL);
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      if (!SkillUtils.isSkillMastered(entity, (ManasSkill)ExtraSkills.SPATIAL_MANIPULATION.get())) {
         return false;
      } else {
         return newEP > 400000.0D;
      }
   }

   public double learningCost() {
      return 200.0D;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return true;
   }

   public int modes() {
      return 4;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      int var10000;
      if (reverse) {
         switch(instance.getMode()) {
         case 2:
            var10000 = 1;
            break;
         case 3:
            var10000 = 2;
            break;
         case 4:
            var10000 = instance.getOrCreateTag().m_128451_("DimensionRay") >= 100 ? 3 : 2;
            break;
         default:
            var10000 = 4;
         }

         return var10000;
      } else {
         switch(instance.getMode()) {
         case 1:
            var10000 = 2;
            break;
         case 2:
            var10000 = instance.getOrCreateTag().m_128451_("DimensionRay") >= 100 ? 3 : 4;
            break;
         case 3:
            var10000 = 4;
            break;
         default:
            var10000 = 1;
         }

         return var10000;
      }
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.spatial_domination.warp_shot");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.spatial_domination.ray");
         break;
      case 3:
         var10000 = Component.m_237115_("tensura.skill.mode.spatial_domination.storm");
         break;
      case 4:
         var10000 = Component.m_237115_("tensura.skill.mode.spatial_domination.fault_field");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      double var10000;
      switch(instance.getMode()) {
      case 2:
         var10000 = 5000.0D;
         break;
      case 3:
         var10000 = 50000.0D;
         break;
      case 4:
         var10000 = 2000.0D;
         break;
      default:
         var10000 = 0.0D;
      }

      return var10000;
   }

   public String modeLearningId(int mode) {
      String var10000;
      switch(mode) {
      case 2:
         var10000 = "DimensionRay";
         break;
      case 3:
         var10000 = "DimensionStorm";
         break;
      case 4:
         var10000 = "FaultField";
         break;
      default:
         var10000 = "None";
      }

      return var10000;
   }

   public boolean canIgnoreCoolDown(ManasSkillInstance instance, LivingEntity entity) {
      if (instance.getMastery() < 0) {
         return false;
      } else {
         return instance.getMode() == 1;
      }
   }

   public void onTakenDamage(ManasSkillInstance instance, LivingDamageEvent event) {
      LivingEntity entity = event.getEntity();
      if (hasFaultField(entity)) {
         DamageSource damageSource = event.getSource();
         if (!damageSource.m_19378_()) {
            if (!DamageSourceHelper.isSpiritual(damageSource)) {
               if (!DamageSourceHelper.isSpatialDamage(damageSource)) {
                  if (!SkillUtils.haveSeveranceAttack(damageSource, entity)) {
                     float damageCanceled = (float)((int)event.getAmount());
                     double lackedMagicule = SkillHelper.outOfMagiculeStillConsume(entity, (double)(damageCanceled * 50.0F));
                     if (lackedMagicule > 0.0D) {
                        damageCanceled = (float)((double)damageCanceled - lackedMagicule / 50.0D);
                        event.setAmount(event.getAmount() - damageCanceled);
                     } else {
                        event.setCanceled(true);
                     }

                  }
               }
            }
         }
      }
   }

   public void onDamageEntity(ManasSkillInstance instance, LivingEntity living, LivingHurtEvent e) {
      if (instance.isToggled()) {
         if (DamageSourceHelper.isSpatialDamage(e.getSource())) {
            e.setAmount(e.getAmount() * 4.0F);
         }

      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      Level level = entity.m_9236_();
      CompoundTag tag = instance.getOrCreateTag();
      int learnPoint;
      Player player;
      switch(instance.getMode()) {
      case 2:
         learnPoint = tag.m_128451_("DimensionRay");
         if (learnPoint < 100) {
            if (SkillHelper.outOfMagicule(entity, instance)) {
               return;
            }

            tag.m_128405_("DimensionRay", learnPoint + SkillUtils.getEarningLearnPoint(instance, entity, true));
            if (entity instanceof Player) {
               player = (Player)entity;
               if (tag.m_128451_("DimensionRay") >= 100) {
                  player.m_5661_(Component.m_237110_("tensura.skill.acquire_learning", new Object[]{this.getModeName(2)}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
               } else {
                  instance.setCoolDown(10);
                  SkillUtils.learningFailPenalty(entity);
                  player.m_5661_(Component.m_237110_("tensura.skill.learn_points_added", new Object[]{this.getModeName(2)}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GREEN)), true);
               }

               player.m_6330_(SoundEvents.f_11871_, SoundSource.PLAYERS, 1.0F, 1.0F);
            }
         } else {
            instance.getOrCreateTag().m_128405_("BeamID", 0);
         }

         instance.markDirty();
         break;
      case 3:
         if (SkillHelper.outOfMagicule(entity, instance)) {
            return;
         }

         learnPoint = tag.m_128451_("DimensionStorm");
         if (learnPoint < 100) {
            tag.m_128405_("DimensionStorm", learnPoint + SkillUtils.getEarningLearnPoint(instance, entity, true));
            if (entity instanceof Player) {
               player = (Player)entity;
               if (tag.m_128451_("DimensionStorm") >= 100) {
                  player.m_5661_(Component.m_237110_("tensura.skill.acquire_learning", new Object[]{this.getModeName(3)}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
               } else {
                  instance.setCoolDown(10);
                  SkillUtils.learningFailPenalty(entity);
                  player.m_5661_(Component.m_237110_("tensura.skill.learn_points_added", new Object[]{this.getModeName(3)}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GREEN)), true);
               }

               player.m_6330_(SoundEvents.f_11871_, SoundSource.PLAYERS, 1.0F, 1.0F);
            }

            instance.markDirty();
            return;
         }

         Entity target = SkillHelper.getTargetingEntity(entity, 30.0D, false, false);
         Vec3 pos;
         if (target != null) {
            pos = target.m_146892_();
         } else {
            BlockHitResult result = SkillHelper.getPlayerPOVHitResult(level, entity, Fluid.NONE, 30.0D);
            pos = result.m_82450_();
         }

         instance.setCoolDown(instance.isMastered(entity) ? 10 : 20);
         level.m_6263_((Player)null, pos.m_7096_(), pos.m_7098_(), pos.m_7094_(), SoundEvents.f_215771_, SoundSource.PLAYERS, 5.0F, 1.0F);
         ((ServerLevel)level).m_8767_(ParticleTypes.f_123747_, pos.m_7096_(), pos.m_7098_(), pos.m_7094_(), 1, 0.0D, 0.0D, 0.0D, 0.0D);
         int rayAmount = instance.isMastered(entity) ? 30 : 20;

         for(int i = 0; i < rayAmount; ++i) {
            Vec3 startOffset = (new Vec3(0.0D, 1.0D - Math.random() * 2.0D, 0.6D)).m_82541_().m_82490_(20.0D).m_82524_(360.0F * (float)i * 0.017453292F / (float)rayAmount);
            SpatialRayProjectile ray = new SpatialRayProjectile(entity.m_9236_(), entity);
            ray.setFollowingOwner(false);
            ray.setLife(20);
            ray.setSize(0.75F);
            ray.setRange(10.0F);
            ray.setSkill(instance);
            Vec3 rayPos = pos.m_82549_(startOffset);
            ray.m_146884_(rayPos.m_82549_(pos.m_82546_(rayPos).m_82541_().m_82490_(10.0D)));
            ray.setTargetPos(pos.m_7096_(), pos.m_7098_(), pos.m_7094_());
            ray.setDamage(instance.isMastered(entity) ? 100.0F : 50.0F);
            ray.setMpCost(this.auraCost(entity, instance));
            entity.m_9236_().m_7967_(ray);
            TensuraParticleHelper.addServerParticlesAroundSelf(ray, ParticleTypes.f_123747_);
         }

         return;
      case 4:
         if (SkillHelper.outOfMagicule(entity, instance)) {
            return;
         }

         learnPoint = tag.m_128451_("FaultField");
         if (learnPoint < 100) {
            tag.m_128405_("FaultField", learnPoint + SkillUtils.getEarningLearnPoint(instance, entity, true));
            if (entity instanceof Player) {
               player = (Player)entity;
               if (tag.m_128451_("FaultField") >= 100) {
                  player.m_5661_(Component.m_237110_("tensura.skill.acquire_learning", new Object[]{this.getModeName(4)}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
               } else {
                  instance.setCoolDown(10);
                  SkillUtils.learningFailPenalty(entity);
                  player.m_5661_(Component.m_237110_("tensura.skill.learn_points_added", new Object[]{this.getModeName(4)}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GREEN)), true);
               }

               player.m_6330_(SoundEvents.f_11871_, SoundSource.PLAYERS, 1.0F, 1.0F);
            }

            instance.markDirty();
         }
      }

   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (instance.getMode() == 2 && !instance.onCoolDown()) {
         CompoundTag tag = instance.getOrCreateTag();
         if (tag.m_128451_("DimensionRay") < 100) {
            return;
         }

         if (!this.hasAttributeApplied(entity, Attributes.f_22279_, "9e5add62-dbfa-4130-ab0b-6612a8f4c4aa")) {
            return;
         }

         instance.setCoolDown(instance.isMastered(entity) ? 7 : 10);
      }

   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      CompoundTag tag = instance.getOrCreateTag();
      if (instance.getMode() == 2) {
         if (tag.m_128451_("DimensionRay") < 100) {
            return false;
         } else {
            if (heldTicks % 60 == 0 && heldTicks > 0) {
               this.addMasteryPoint(instance, entity);
            }

            double cost = this.magiculeCost(entity, instance);
            BeamProjectile.spawnLastingBeam((EntityType)TensuraEntityTypes.SPATIAL_RAY.get(), instance.isMastered(entity) ? 100.0F : 50.0F, 0.5F, entity, instance, cost, cost, heldTicks);
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11862_, SoundSource.PLAYERS, 0.8F, 0.5F);
            if (heldTicks > 60) {
               instance.setCoolDown(instance.isMastered(entity) ? 7 : 10);
               this.removeHeldAttributeModifiers(instance, entity);
               return false;
            } else {
               return true;
            }
         }
      } else if (instance.getMode() != 4) {
         return false;
      } else if (tag.m_128451_("FaultField") < 100) {
         return false;
      } else if (heldTicks % 20 == 0 && SkillHelper.outOfMagicule(entity, instance)) {
         return false;
      } else {
         if (heldTicks % 60 == 0 && heldTicks > 0) {
            this.addMasteryPoint(instance, entity);
         }

         return true;
      }
   }

   public void addHeldAttributeModifiers(ManasSkillInstance instance, LivingEntity entity) {
      if (instance.getMode() == 4 && instance.getOrCreateTag().m_128451_("FaultField") >= 100) {
         super.addHeldAttributeModifiers(instance, entity);
      }
   }

   public static boolean hasFaultField(LivingEntity entity) {
      if (entity instanceof HinataSakaguchiEntity) {
         HinataSakaguchiEntity hinata = (HinataSakaguchiEntity)entity;
         if (hinata.getPhase() == 1) {
            return true;
         }
      }

      AttributeInstance attributeInstance = entity.m_21051_(Attributes.f_22279_);
      if (attributeInstance == null) {
         return false;
      } else {
         return attributeInstance.m_22111_(UUID.fromString("9e5add62-dbfa-4130-ab0b-6612a8f4c4aa")) != null;
      }
   }
}
