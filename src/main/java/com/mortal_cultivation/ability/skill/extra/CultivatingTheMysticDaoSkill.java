package com.mortal_cultivation.ability.skill.extra;

import com.mortal_cultivation.MortalCultivation;
import com.mortal_cultivation.entity.TaoistArrayEntity;
import com.mortal_cultivation.registry.CultivationEntities;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.util.RandomSource;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.phys.Vec3;

/**
 * 修玄技能 - 额外技能
 * 盘坐虚空，神游太虚，以玄心感应天地灵机，淬炼神识
 * 
 * 这是一个修仙专用的冥想技能，用于提升修士的神识和感知能力
 */
public class CultivatingTheMysticDaoSkill extends Skill {

    // 技能参数配置
    private final double skillCastCost = 0.0;      // 施法消耗的魔素
    private final double learnCost = 2.0;         // 学习难度 (越高越难)

    // 道法阵管理
    private TaoistArrayEntity currentArray = null;
    private Vec3 lastPlayerPosition = null; // 记录玩家上一次的位置



    public CultivatingTheMysticDaoSkill() {
        super(SkillType.EXTRA);
    }

    /**
     * 获取技能图标路径
     * @return 技能图标的资源位置
     */
    @Override
    public ResourceLocation getSkillIcon() {
        return new ResourceLocation(MortalCultivation.MODID, "textures/skill/extra/cultivating_the_mystic_dao.png");
    }

    /**
     * 技能解锁条件
     * 此技能无法通过EP解锁，需要其他方式获得
     *
     * @param entity 玩家实体
     * @param curEP 当前EP值
     * @return 是否满足解锁条件
     */
    @Override
    public boolean meetEPRequirement(Player entity, double curEP) {
        return false;
    }

    /**
     * 学习技能的难度
     * @return 学习成本
     */
    @Override
    public double learningCost() {
        return learnCost;
    }

    /**
     * 技能的魔素消耗
     * @param entity 使用者
     * @param instance 技能实例
     * @return 魔素消耗量
     */
    @Override
    public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
        return skillCastCost;
    }

    /**
     * 技能是否可以被切换 (开启/关闭)
     * 只有在技能精通后才能切换
     * 
     * @param instance 技能实例
     * @param living 使用者
     * @return 是否可以切换
     */
    @Override
    public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
        return instance.isMastered(living);
    }

    /**
     * 技能是否可以持续运行 (tick)
     * 只有在精通且开启状态下才能持续运行
     * 
     * @param instance 技能实例
     * @param entity 使用者
     * @return 是否可以持续运行
     */
    @Override
    public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
        return instance.isMastered(entity) && instance.isToggled();
    }

    /**
     * 技能按下时的逻辑
     * 目前暂时不实现具体功能
     *
     * @param instance 技能实例
     * @param entity 使用者
     */
    @Override
    public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
        // 检查魔素是否足够
        if (SkillHelper.outOfMagicule(entity, this.magiculeCost(entity, instance))) {
            return;
        }

        // TODO: 实现修玄技能的具体效果
        // 例如：提升感知范围、增强神识、获得特殊视觉效果等
    }

    /**
     * 技能持续运行时的逻辑 (每tick调用)
     * 在技能开启状态下持续消耗魔素
     * 
     * @param instance 技能实例
     * @param entity 使用者
     */
    @Override
    public void onTick(ManasSkillInstance instance, LivingEntity entity) {
        // 检查魔素是否足够维持技能
        if (SkillHelper.outOfMagicule(entity, this.magiculeCost(entity, instance) * 2.0)) {
            // 魔素不足时自动关闭技能
            instance.setToggled(false);
            return;
        }

        // TODO: 实现修玄技能的持续效果
        // 例如：持续恢复精神力、提升经验获取、增强感知等
        
        super.onTick(instance, entity);
    }

    /**
     * 技能被持续按住时的逻辑
     *
     * @param instance 技能实例
     * @param entity 使用者
     * @param heldTicks 持续按住的tick数
     * @return 是否继续保持激活状态
     */
    @Override
    public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
        // 在开始时创建道法阵 (只在服务器端)
        if (heldTicks == 1 && !entity.level.isClientSide) {
            createTaoistArray(entity);
        }

        // 每20 ticks (1秒) 检查一次魔素
        if (heldTicks % 20 == 0 && SkillHelper.outOfMagicule(entity, instance)) {
            // 移除道法阵
            if (!entity.level.isClientSide) {
                removeTaoistArray();
            }
            return false;
        }

        // 每tick都执行的效果
        if (!entity.level.isClientSide) {
            // 检查并管理道法阵
            manageArrayPosition(entity);

            // 生成白色粒子效果 (每5tick一次，减少粒子数量)
            if (heldTicks % 5 == 0) {
                spawnMeditationParticles(entity);
            }

            // 每20 ticks (1秒) 恢复1%的魔素、斗气、生命
            if (heldTicks % 20 == 0) {
                restorePlayerStats(entity);
            }
        }

        // 每60 ticks (3秒) 增加一次熟练度
        if (heldTicks % 60 == 0 && heldTicks > 0) {
            this.addMasteryPoint(instance, entity);
        }

        // TODO: 实现修玄技能的持续按住效果
        // 例如：深度冥想状态、加速神识淬炼等

        return true;
    }

    @Override
    public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
        // 当技能释放时移除道法阵 (只在服务器端)
        if (!entity.level.isClientSide) {
            removeTaoistArray();
        }
    }

    /**
     * 创建道法阵实体
     */
    private void createTaoistArray(LivingEntity entity) {
        try {
            // 移除已存在的道法阵
            removeTaoistArray();

            // 在玩家脚下创建道法阵，往上4个像素(0.25个方块)
            Vec3 playerPos = entity.position();
            Vec3 arrayPos = new Vec3(playerPos.x, playerPos.y + 0.25, playerPos.z);

            currentArray = new TaoistArrayEntity(CultivationEntities.TAOIST_ARRAY.get(), entity.level);
            currentArray.setPos(arrayPos.x, arrayPos.y, arrayPos.z);

            // 添加到世界中
            entity.level.addFreshEntity(currentArray);

        } catch (Exception e) {
            // 静默处理创建失败
        }
    }

    /**
     * 移除道法阵实体
     */
    private void removeTaoistArray() {
        try {
            if (currentArray != null && !currentArray.isRemoved()) {
                currentArray.forceRemove(); // 使用强制移除方法
            }
            currentArray = null;
        } catch (Exception e) {
            // 静默处理移除失败
        }
    }

    /**
     * 生成冥想时的白色粒子效果
     */
    private void spawnMeditationParticles(LivingEntity entity) {
        if (!(entity.level instanceof ServerLevel serverLevel)) {
            return;
        }

        RandomSource random = entity.getRandom();
        Vec3 entityPos = entity.position();

        // 在玩家周围生成少量白色粒子
        for (int i = 0; i < 3; i++) { // 每次生成3个粒子
            double offsetX = (random.nextDouble() - 0.5) * 2.0; // -1.0 到 1.0
            double offsetY = random.nextDouble() * 2.0; // 0 到 2.0 (玩家高度)
            double offsetZ = (random.nextDouble() - 0.5) * 2.0; // -1.0 到 1.0

            double particleX = entityPos.x + offsetX;
            double particleY = entityPos.y + offsetY;
            double particleZ = entityPos.z + offsetZ;

            // 粒子向上缓慢移动
            double velocityX = (random.nextDouble() - 0.5) * 0.02;
            double velocityY = 0.05 + random.nextDouble() * 0.03; // 向上移动
            double velocityZ = (random.nextDouble() - 0.5) * 0.02;

            serverLevel.sendParticles(
                ParticleTypes.WHITE_ASH, // 使用白色灰烬粒子
                particleX, particleY, particleZ,
                1, // 粒子数量
                velocityX, velocityY, velocityZ,
                0.01 // 速度
            );
        }
    }

    /**
     * 恢复玩家的魔素、斗气、生命 (每秒1%)
     */
    private void restorePlayerStats(LivingEntity entity) {
        if (!(entity instanceof Player player)) {
            return;
        }

        try {
            // 恢复生命值 (1%)
            float maxHealth = player.getMaxHealth();
            float currentHealth = player.getHealth();
            float healAmount = maxHealth * 0.01f; // 1%

            if (currentHealth < maxHealth) {
                player.heal(healAmount);
            }

            // 恢复魔素 (使用Tensura的静态方法)
            try {
                // 获取当前魔素和基础魔素
                double currentMagicule = TensuraPlayerCapability.getMagicule(player);
                double baseMagicule = TensuraPlayerCapability.getBaseMagicule(player);
                double magiculeRestore = baseMagicule * 0.01; // 1%

                if (currentMagicule < baseMagicule) {
                    double newMagicule = Math.min(baseMagicule, currentMagicule + magiculeRestore);
                    // 使用Tensura的静态方法设置魔素
                    TensuraPlayerCapability.setMagicule(player, newMagicule);
                }
            } catch (Exception e) {
                // 如果Tensura API不可用，静默处理
            }

        } catch (Exception e) {
            // 静默处理错误
        }
    }

    /**
     * 管理道法阵位置 - 检测玩家位置变动并相应更新或重新创建法阵
     */
    private void manageArrayPosition(LivingEntity entity) {
        try {
            Vec3 currentPlayerPos = entity.position();

            // 强化法阵存在检测 - 检查法阵是否真的存在且有效
            boolean arrayExists = isArrayValid(entity);

            // 如果法阵不存在或无效，立即重新创建
            if (!arrayExists) {
                recreateArray(entity, currentPlayerPos);
                return;
            }

            // 检查玩家位置是否发生变动
            boolean positionChanged = false;
            if (lastPlayerPosition == null) {
                positionChanged = true;
            } else {
                // 检查位置变化（容忍度0.01方块，避免微小浮动导致频繁更新）
                double deltaX = Math.abs(currentPlayerPos.x - lastPlayerPosition.x);
                double deltaY = Math.abs(currentPlayerPos.y - lastPlayerPosition.y);
                double deltaZ = Math.abs(currentPlayerPos.z - lastPlayerPosition.z);

                if (deltaX > 0.01 || deltaY > 0.01 || deltaZ > 0.01) {
                    positionChanged = true;
                }
            }

            // 如果玩家位置发生变动，更新法阵位置
            if (positionChanged) {
                // 在更新前再次检查法阵是否有效
                if (isArrayValid(entity)) {
                    updateArrayToPlayerPosition(entity, currentPlayerPos);
                    lastPlayerPosition = currentPlayerPos;
                } else {
                    // 如果法阵在更新过程中消失，重新创建
                    recreateArray(entity, currentPlayerPos);
                }
            }

        } catch (Exception e) {
            // 如果出现任何错误，强制重新创建法阵
            recreateArray(entity, entity.position());
        }
    }

    /**
     * 检查法阵是否有效存在
     */
    private boolean isArrayValid(LivingEntity entity) {
        try {
            if (currentArray == null) {
                return false;
            }

            if (currentArray.isRemoved()) {
                currentArray = null;
                return false;
            }

            // 检查法阵是否还在世界中
            if (!entity.level.getEntities().contains(currentArray)) {
                currentArray = null;
                return false;
            }

            // 检查法阵距离玩家是否过远（可能被传送到错误位置）
            double distance = currentArray.distanceTo(entity);
            if (distance > 10.0) { // 如果距离超过10个方块，认为法阵丢失
                currentArray.discard();
                currentArray = null;
                return false;
            }

            return true;
        } catch (Exception e) {
            currentArray = null;
            return false;
        }
    }

    /**
     * 重新创建法阵
     */
    private void recreateArray(LivingEntity entity, Vec3 playerPos) {
        try {
            // 清理旧法阵
            if (currentArray != null) {
                try {
                    currentArray.discard();
                } catch (Exception e) {
                    // 忽略清理错误
                }
                currentArray = null;
            }

            // 创建新法阵
            createTaoistArray(entity);
            lastPlayerPosition = playerPos;

        } catch (Exception e) {
            // 静默处理重建失败
            currentArray = null;
            lastPlayerPosition = null;
        }
    }

    /**
     * 将道法阵更新到玩家位置
     */
    private void updateArrayToPlayerPosition(LivingEntity entity, Vec3 playerPos) {
        try {
            if (currentArray != null && !currentArray.isRemoved()) {
                // 计算新的道法阵位置 (玩家脚下往上0.25个方块)
                Vec3 newArrayPos = new Vec3(playerPos.x, playerPos.y + 0.25, playerPos.z);

                // 使用多种方法强制更新道法阵位置
                currentArray.setPos(newArrayPos.x, newArrayPos.y, newArrayPos.z);
                currentArray.moveTo(newArrayPos.x, newArrayPos.y, newArrayPos.z);
                currentArray.setPosRaw(newArrayPos.x, newArrayPos.y, newArrayPos.z);
                currentArray.setDeltaMovement(0, 0, 0); // 清除任何运动

                // 如果距离玩家太远，强制传送到正确位置
                double distance = currentArray.distanceTo(entity);
                if (distance > 1.0) {
                    currentArray.teleportTo(newArrayPos.x, newArrayPos.y, newArrayPos.z);
                }
            }
        } catch (Exception e) {
            // 如果更新失败，重新创建法阵
            recreateArray(entity, playerPos);
        }
    }
}
