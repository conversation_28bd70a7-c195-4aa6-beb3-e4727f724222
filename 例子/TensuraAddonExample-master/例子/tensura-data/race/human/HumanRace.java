package com.github.manasmods.tensura.race.human;

import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.registry.race.TensuraRaces;
import com.github.manasmods.tensura.util.JumpPowerHelper;
import com.mojang.datafixers.util.Pair;
import java.util.ArrayList;
import java.util.List;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.player.Player;
import org.jetbrains.annotations.Nullable;

public class HumanRace extends Race {
   public HumanRace() {
      super(Race.Difficulty.HARD);
   }

   public HumanRace(Race.Difficulty difficulty) {
      super(difficulty);
   }

   public double getBaseHealth() {
      return 20.0D;
   }

   public float getPlayerSize() {
      return 2.0F;
   }

   public double getBaseAttackDamage() {
      return 1.0D;
   }

   public double getBaseAttackSpeed() {
      return 4.0D;
   }

   public double getKnockbackResistance() {
      return 0.0D;
   }

   public double getJumpHeight() {
      return JumpPowerHelper.defaultPlayer();
   }

   public double getMovementSpeed() {
      return 0.1D;
   }

   public double getSprintSpeed() {
      return 0.13D;
   }

   public Pair<Double, Double> getBaseAuraRange() {
      return Pair.of(760.0D, 1140.0D);
   }

   public Pair<Double, Double> getBaseMagiculeRange() {
      return Pair.of(50.0D, 70.0D);
   }

   @Nullable
   public Race getDefaultEvolution(Player player) {
      return (Race)TensuraRaces.ENLIGHTENED_HUMAN.get();
   }

   @Nullable
   public Race getAwakeningEvolution(Player player) {
      return (Race)TensuraRaces.HUMAN_SAINT.get();
   }

   @Nullable
   public Race getHarvestFestivalEvolution(Player player) {
      return (Race)TensuraRaces.ENLIGHTENED_HUMAN.get();
   }

   public List<Race> getNextEvolutions(Player player) {
      List<Race> list = new ArrayList();
      list.add((Race)TensuraRaces.ENLIGHTENED_HUMAN.get());
      list.add((Race)TensuraRaces.VAMPIRE.get());
      return list;
   }

   public double getEvolutionPercentage(Player player) {
      int chance = 0;
      if (player.m_21023_(MobEffects.f_19613_)) {
         chance += 20;
      }

      if (player.m_21023_(MobEffects.f_19606_)) {
         chance += 20;
      }

      if (player.m_21023_(MobEffects.f_19607_)) {
         chance += 20;
      }

      MobEffectInstance regeneration = player.m_21124_(MobEffects.f_19605_);
      if (regeneration != null && regeneration.m_19564_() >= 1) {
         chance += 20;
      }

      MobEffectInstance absorption = player.m_21124_(MobEffects.f_19617_);
      if (absorption != null && absorption.m_19564_() >= 3) {
         chance += 20;
      }

      return (double)chance;
   }
}
