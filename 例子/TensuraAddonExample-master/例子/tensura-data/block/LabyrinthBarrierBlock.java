package com.github.manasmods.tensura.block;

import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.client.TensuraGUIHelper;
import com.github.manasmods.tensura.entity.ElementalColossusEntity;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.github.manasmods.tensura.world.savedata.LabyrinthSaveData;
import javax.annotation.Nullable;
import net.minecraft.core.BlockPos;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.util.RandomSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.projectile.Projectile;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.context.BlockPlaceContext;
import net.minecraft.world.level.BlockGetter;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelReader;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.RenderShape;
import net.minecraft.world.level.block.SoundType;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.properties.BlockStateProperties;
import net.minecraft.world.level.block.state.properties.BooleanProperty;
import net.minecraft.world.level.block.state.properties.Property;
import net.minecraft.world.level.material.Fluid;
import net.minecraft.world.level.material.MaterialColor;
import net.minecraft.world.level.material.PushReaction;
import net.minecraft.world.level.material.Material.Builder;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraft.world.phys.shapes.CollisionContext;
import net.minecraft.world.phys.shapes.EntityCollisionContext;
import net.minecraft.world.phys.shapes.Shapes;
import net.minecraft.world.phys.shapes.VoxelShape;

public class LabyrinthBarrierBlock extends SimpleBlock {
   public static final BooleanProperty BLOCK_MOTION;

   public LabyrinthBarrierBlock() {
      super((new Builder(MaterialColor.f_76398_)).m_76359_(), (properties) -> {
         return properties.m_60960_(SimpleLeaves::never).m_60971_((blockState, blockGetter, blockPos) -> {
            return false;
         }).m_60922_((blockState, blockGetter, blockPos, entityType) -> {
            return false;
         }).m_222994_().m_60988_().m_60955_().m_60918_(SoundType.f_154654_).m_60913_(-1.0F, 3600000.0F);
      });
      this.m_49959_((BlockState)((BlockState)this.f_49792_.m_61090_()).m_61124_(BLOCK_MOTION, Boolean.FALSE));
   }

   public VoxelShape m_7952_(BlockState pState, BlockGetter pLevel, BlockPos pPos) {
      return Shapes.m_83040_();
   }

   public RenderShape m_7514_(BlockState pState) {
      return RenderShape.INVISIBLE;
   }

   public void m_214162_(BlockState pState, Level pLevel, BlockPos pos, RandomSource pRandom) {
      TensuraGUIHelper.spawnMarkerParticle(pLevel, pState, pos, (Item)TensuraBlocks.Items.LABYRINTH_BARRIER_BLOCK.get());
   }

   public VoxelShape m_5939_(BlockState pState, BlockGetter pLevel, BlockPos pPos, CollisionContext pContext) {
      Entity var10000;
      if (pContext instanceof EntityCollisionContext) {
         EntityCollisionContext entityCollision = (EntityCollisionContext)pContext;
         var10000 = entityCollision.m_193113_();
      } else {
         var10000 = null;
      }

      Entity entity = var10000;
      if (entity instanceof Projectile) {
         return Shapes.m_83144_();
      } else {
         return this.canGoThrough(pState, entity) ? Shapes.m_83040_() : Shapes.m_83144_();
      }
   }

   public void m_7892_(BlockState pState, Level pLevel, BlockPos pPos, Entity target) {
      if (!this.canGoThrough(pState, target)) {
         if (pLevel instanceof ServerLevel) {
            ServerLevel serverLevel = (ServerLevel)pLevel;
            LabyrinthSaveData var6 = LabyrinthSaveData.get(serverLevel.m_7654_().m_129783_());
            Vec3 throwVec = var6.getColossusPos().m_82546_(target.m_20182_()).m_82541_().m_82490_(1.5D);
            target.m_20334_(throwVec.m_7096_(), 0.0D, throwVec.m_7094_());
            if (target instanceof ElementalColossusEntity) {
               ElementalColossusEntity colossus = (ElementalColossusEntity)target;
               colossus.m_21661_();
            }

            target.f_19864_ = true;
         }
      }
   }

   public InteractionResult m_6227_(BlockState pState, Level pLevel, BlockPos pPos, Player pPlayer, InteractionHand pHand, BlockHitResult pHit) {
      if (pPlayer.m_7500_() && pPlayer.m_21120_(pHand).m_41619_()) {
         pLevel.m_46597_(pPos, (BlockState)pState.m_61124_(BLOCK_MOTION, !(Boolean)pState.m_61143_(BLOCK_MOTION)));
         return InteractionResult.SUCCESS;
      } else {
         return super.m_6227_(pState, pLevel, pPos, pPlayer, pHand, pHit);
      }
   }

   public boolean m_180643_(BlockState pState, BlockGetter pLevel, BlockPos pPos) {
      return true;
   }

   public boolean m_5946_(BlockState pState, Fluid pFluid) {
      return false;
   }

   public boolean m_6864_(BlockState pState, BlockPlaceContext pUseContext) {
      return false;
   }

   public boolean m_7898_(BlockState pState, LevelReader pLevel, BlockPos pPos) {
      return true;
   }

   public boolean collisionExtendsVertically(BlockState state, BlockGetter level, BlockPos pos, Entity collidingEntity) {
      return true;
   }

   protected void m_142387_(Level pLevel, Player pPlayer, BlockPos pPos, BlockState pState) {
   }

   public boolean m_7420_(BlockState pState, BlockGetter pLevel, BlockPos pPos) {
      return true;
   }

   protected void m_7926_(net.minecraft.world.level.block.state.StateDefinition.Builder<Block, BlockState> pBuilder) {
      pBuilder.m_61104_(new Property[]{BLOCK_MOTION});
   }

   public PushReaction m_5537_(BlockState pState) {
      return PushReaction.IGNORE;
   }

   private boolean canGoThrough(BlockState pState, @Nullable Entity entity) {
      if (entity == null) {
         return true;
      } else {
         if (entity instanceof ElementalColossusEntity) {
            ElementalColossusEntity colossus = (ElementalColossusEntity)entity;
            if (!colossus.m_21824_()) {
               return false;
            }
         }

         if (this.isColossusStartedBlocking(entity)) {
            return false;
         } else if ((Boolean)pState.m_61143_(BLOCK_MOTION)) {
            return this.isColossusWonButEmpty(entity) ? true : LabyrinthSaveData.isEntityPassedColossus(entity);
         } else {
            return true;
         }
      }
   }

   private boolean isColossusStartedBlocking(Entity entity) {
      if (entity instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)entity;
         if (entity.f_19853_.m_7654_() != null && !LabyrinthSaveData.get(entity.f_19853_.m_7654_().m_129783_()).isHavingColossus() && TensuraEffectsCapability.isColossusStarted(living)) {
            LabyrinthSaveData.removeStartedEntity(living);
            return false;
         } else {
            return TensuraEffectsCapability.isColossusStarted(living);
         }
      } else {
         return false;
      }
   }

   private boolean isColossusWonButEmpty(Entity entity) {
      if (entity instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)entity;
         return entity.f_19853_.m_7654_() != null && !LabyrinthSaveData.get(entity.f_19853_.m_7654_().m_129783_()).isHavingColossus() && TensuraEffectsCapability.isColossusWon(living) ? true : TensuraEffectsCapability.isColossusWon(living);
      } else {
         return false;
      }
   }

   static {
      BLOCK_MOTION = BlockStateProperties.f_61431_;
   }
}
