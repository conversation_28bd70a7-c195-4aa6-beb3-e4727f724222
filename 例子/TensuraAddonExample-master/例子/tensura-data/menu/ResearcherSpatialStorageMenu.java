package com.github.manasmods.tensura.menu;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.ISpatialStorage;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.data.pack.GearEPCount;
import com.github.manasmods.tensura.data.pack.TensuraData;
import com.github.manasmods.tensura.menu.container.SpatialStorageContainer;
import com.github.manasmods.tensura.menu.container.TensuraCraftingContainer;
import com.github.manasmods.tensura.menu.slot.spatial.SpatialSlot;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import java.util.Iterator;
import java.util.Objects;
import java.util.Optional;
import net.minecraft.core.NonNullList;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.protocol.game.ClientboundContainerSetSlotPacket;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.inventory.AbstractContainerMenu;
import net.minecraft.world.inventory.ContainerLevelAccess;
import net.minecraft.world.inventory.DataSlot;
import net.minecraft.world.inventory.MenuType;
import net.minecraft.world.inventory.ResultContainer;
import net.minecraft.world.inventory.Slot;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.crafting.CraftingRecipe;
import net.minecraft.world.item.crafting.RecipeType;
import net.minecraft.world.item.crafting.SmeltingRecipe;
import net.minecraftforge.common.ForgeHooks;
import net.minecraftforge.event.ForgeEventFactory;
import net.minecraftforge.registries.ForgeRegistries;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class ResearcherSpatialStorageMenu extends AbstractContainerMenu {
   private static final Logger log = LogManager.getLogger(ResearcherSpatialStorageMenu.class);
   private static final int HOTBAR_SLOT_COUNT = 9;
   private static final int INVENTORY_ROW_COUNT = 3;
   private static final int INVENTORY_COLUMN_COUNT = 9;
   private final ManasSkill skill;
   private final ContainerLevelAccess access;
   private final Player player;
   private final SpatialStorageContainer container;
   private final SpatialStorageContainer furnaceInputContainer;
   private final DataSlot startIndex;
   private final Slot craftingResultSlot;
   private final Slot furnaceInputSlot;
   private final Slot furnaceResultSlot;
   private final TensuraCraftingContainer craftingGridContainer;
   private final ResultContainer craftingResultContainer;
   private final ResultContainer furnaceResultContainer;
   private int removeCount;

   public ResearcherSpatialStorageMenu(int id, Inventory inv, Player player, SpatialStorageContainer container, ManasSkill skill) {
      super((MenuType)null, id);
      this.access = ContainerLevelAccess.f_39287_;
      this.startIndex = DataSlot.m_39401_();
      this.craftingResultContainer = new ResultContainer();
      this.skill = skill;
      this.player = player;
      this.container = container;
      m_38869_(container, this.getStorageSize());
      this.m_38895_(this.startIndex).m_6422_(0);
      container.m_5856_(player);
      this.craftingGridContainer = new TensuraCraftingContainer(this, 3, 3);
      this.craftingResultSlot = new Slot(this.craftingResultContainer, 0, 232, 58) {
         public boolean m_5857_(ItemStack pStack) {
            return false;
         }

         public void m_142406_(Player pPlayer, ItemStack pStack) {
            super.m_142406_(pPlayer, pStack);
            if (!pStack.m_41619_()) {
               ResearcherSpatialStorageMenu.this.itemCrafted(pStack);
               ResearcherSpatialStorageMenu.this.slotChangedCraftingGrid();
            }
         }

         @NotNull
         public ItemStack m_6201_(int pAmount) {
            if (this.m_6657_()) {
               ResearcherSpatialStorageMenu var10000 = ResearcherSpatialStorageMenu.this;
               var10000.removeCount += Math.min(pAmount, this.m_7993_().m_41613_());
            }

            return super.m_6201_(pAmount);
         }

         protected void m_7169_(ItemStack pStack, int pAmount) {
            ResearcherSpatialStorageMenu var10000 = ResearcherSpatialStorageMenu.this;
            var10000.removeCount += pAmount;
            if (!pStack.m_41619_()) {
               ResearcherSpatialStorageMenu.this.itemCrafted(pStack);
               if (!ResearcherSpatialStorageMenu.this.craftingGridContainer.m_7983_()) {
                  ResearcherSpatialStorageMenu.this.slotChangedCraftingGrid();
               }

               this.m_5845_(pStack);
            }
         }

         protected void m_6405_(int pNumItemsCrafted) {
            ResearcherSpatialStorageMenu var10000 = ResearcherSpatialStorageMenu.this;
            var10000.removeCount += pNumItemsCrafted;
         }
      };
      this.furnaceInputContainer = new SpatialStorageContainer(1, 128);
      this.furnaceInputSlot = new Slot(this.furnaceInputContainer, 0, 183, 107) {
         public void m_5852_(ItemStack pStack) {
            super.m_5852_(pStack);
            ResearcherSpatialStorageMenu.this.slotChangedFurnaceInput();
         }
      };
      this.furnaceResultContainer = new ResultContainer() {
         public int m_6893_() {
            return ResearcherSpatialStorageMenu.this.getMaxStack();
         }
      };
      this.furnaceResultSlot = new Slot(this.furnaceResultContainer, 0, 224, 107) {
         public boolean m_5857_(ItemStack pStack) {
            return false;
         }

         public void m_142406_(Player pPlayer, ItemStack pStack) {
            super.m_142406_(pPlayer, pStack);
            if (!ResearcherSpatialStorageMenu.this.furnaceInputContainer.m_7983_()) {
               ResearcherSpatialStorageMenu.this.slotChangedFurnaceInput();
            }

         }
      };
      this.addSpatialSlots();
      this.addFurnaceSlots();
      this.addCraftingSlots();
      this.addPlayerInventory(inv);
   }

   public boolean m_6366_(Player pPlayer, int pId) {
      if (this.isValidRecipeIndex(pId)) {
         this.startIndex.m_6422_(pId);
         this.container.m_6596_();
         this.m_6199_(this.container);
         this.f_38839_.forEach(Slot::m_6654_);
         return true;
      } else {
         return super.m_6366_(pPlayer, pId);
      }
   }

   private void slotChangedCraftingGrid() {
      Player var2 = this.player;
      if (var2 instanceof ServerPlayer) {
         ServerPlayer serverPlayer = (ServerPlayer)var2;
         ServerLevel level = serverPlayer.m_9236_();
         MinecraftServer server = serverPlayer.m_20194_();
         if (server != null) {
            ItemStack stack = ItemStack.f_41583_;
            Optional<CraftingRecipe> optional = server.m_129894_().m_44015_(RecipeType.f_44107_, this.craftingGridContainer, level);
            if (optional.isEmpty()) {
               this.craftingResultContainer.m_6211_();
               this.craftingResultContainer.m_6596_();
            } else {
               CraftingRecipe recipe = (CraftingRecipe)optional.get();
               if (this.craftingResultContainer.m_40135_(level, serverPlayer, recipe)) {
                  stack = recipe.m_5874_(this.craftingGridContainer);
               }

               this.craftingResultContainer.m_6836_(0, stack);
               this.m_150404_(0, stack);
               serverPlayer.f_8906_.m_9829_(new ClientboundContainerSetSlotPacket(this.f_38840_, this.m_182425_(), 0, stack));
            }
         }
      }
   }

   private void slotChangedFurnaceInput() {
      Player var2 = this.player;
      if (var2 instanceof ServerPlayer) {
         ServerPlayer serverPlayer = (ServerPlayer)var2;
         ServerLevel level = serverPlayer.m_9236_();
         MinecraftServer server = serverPlayer.m_20194_();
         if (server != null) {
            Optional<SmeltingRecipe> optional = server.m_129894_().m_44015_(RecipeType.f_44108_, this.furnaceInputContainer, level);
            if (!optional.isEmpty()) {
               SmeltingRecipe recipe = (SmeltingRecipe)optional.get();
               ItemStack stack = recipe.m_5874_(this.furnaceInputContainer);
               ItemStack resultContainerItem = this.furnaceResultContainer.m_8020_(0);
               if (resultContainerItem.m_41619_() || resultContainerItem.m_150930_(stack.m_41720_())) {
                  if (this.furnaceResultContainer.m_40135_(level, serverPlayer, recipe)) {
                     int countToSet = 0;
                     int countToRemove = 0;
                     int recipeItemCount = stack.m_41613_();
                     int resultContainerItemCount = resultContainerItem.m_41613_();
                     int size = this.furnaceInputContainer.m_8020_(0).m_41613_();
                     if (resultContainerItemCount != this.getMaxStack()) {
                        while(resultContainerItemCount + recipeItemCount + countToSet <= this.getMaxStack() && size - countToRemove > 0) {
                           countToSet += recipeItemCount;
                           ++countToRemove;
                        }

                        stack.m_41764_(countToSet + resultContainerItemCount);
                        this.furnaceInputContainer.m_8020_(0).m_41774_(countToRemove);
                        this.furnaceResultContainer.m_6836_(0, stack);
                        this.m_150404_(0, stack);
                        serverPlayer.f_8906_.m_9829_(new ClientboundContainerSetSlotPacket(this.f_38840_, this.m_182425_(), 0, stack));
                        this.furnaceResultContainer.m_8015_(serverPlayer);
                        this.player.m_6330_(SoundEvents.f_12031_, SoundSource.NEUTRAL, 1.0F, 1.0F);
                     }
                  }
               }
            }
         }
      }
   }

   private boolean isValidRecipeIndex(int pRecipeIndex) {
      return pRecipeIndex >= 0 && pRecipeIndex < this.getStorageSize();
   }

   public int getStartIndex() {
      return this.startIndex.m_6501_();
   }

   public int getStorageSize() {
      return this.container.m_6643_();
   }

   public int getMaxStack() {
      return this.container.m_6893_();
   }

   private void addPlayerInventory(Inventory playerInventory) {
      int i;
      for(i = 0; i < 3; ++i) {
         for(int l = 0; l < 9; ++l) {
            this.m_38897_(new Slot(playerInventory, l + i * 9 + 9, 8 + l * 18, 145 + i * 18));
         }
      }

      for(i = 0; i < 9; ++i) {
         this.m_38897_(new Slot(playerInventory, i, 8 + i * 18, 203));
      }

   }

   private void addSpatialSlots() {
      for(int x = 0; x < 5; ++x) {
         for(int y = 0; y < 8; ++y) {
            this.m_38897_(new SpatialSlot(this, this.container, y + x * 8, 8 + y * 18, 44 + x * 18));
         }
      }

   }

   private void addCraftingSlots() {
      for(int x = 0; x < 3; ++x) {
         for(int y = 0; y < 3; ++y) {
            this.m_38897_(new Slot(this.craftingGridContainer, y + x * 3, 174 + y * 18, 40 + x * 18) {
               public void m_5852_(ItemStack pStack) {
                  super.m_5852_(pStack);
                  ResearcherSpatialStorageMenu.this.slotChangedCraftingGrid();
               }
            });
         }
      }

      this.m_38897_(this.craftingResultSlot);
   }

   private void addFurnaceSlots() {
      this.m_38897_(this.furnaceInputSlot);
      this.m_38897_(this.furnaceResultSlot);
   }

   private void itemCrafted(ItemStack stack) {
      if (this.removeCount > 0) {
         stack.m_41678_(this.player.f_19853_, this.player, this.removeCount);
         ForgeEventFactory.firePlayerCraftingEvent(this.player, stack, this.craftingGridContainer);
      }

      this.removeCount = 0;
      if (this.skill == UniqueSkills.GODLY_CRAFTSMAN.get()) {
         Iterator var2 = TensuraData.getGearEP().iterator();

         while(var2.hasNext()) {
            GearEPCount gearEPCount = (GearEPCount)var2.next();
            if (Objects.equals(ForgeRegistries.ITEMS.getKey(stack.m_41720_()), gearEPCount.getItem())) {
               CompoundTag tag = stack.m_41784_();
               double maxEP = (double)gearEPCount.getMaxEP();
               if (tag.m_128459_("MaxEP") < maxEP) {
                  tag.m_128347_("MaxEP", maxEP);
               }

               double chance = (double)this.player.m_217043_().m_216339_(1, 5) * 0.25D;
               if (SkillUtils.isSkillMastered(this.player, (ManasSkill)UniqueSkills.GODLY_CRAFTSMAN.get())) {
                  chance = Math.min(chance + 0.25D, 1.0D);
               }

               double EP = (double)gearEPCount.getMinEP() + chance * (maxEP - (double)gearEPCount.getMinEP());
               tag.m_128347_("EP", EP);
               tag.m_128347_("durabilityEP", EP);
               break;
            }
         }
      }

      ForgeHooks.setCraftingPlayer(this.player);
      NonNullList<ItemStack> list = this.player.f_19853_.m_7465_().m_44069_(RecipeType.f_44107_, this.craftingGridContainer, this.player.f_19853_);
      ForgeHooks.setCraftingPlayer((Player)null);

      for(int i = 0; i < list.size(); ++i) {
         ItemStack slotStack = this.craftingGridContainer.m_8020_(i);
         ItemStack recipeStack = (ItemStack)list.get(i);
         if (!slotStack.m_41619_()) {
            this.craftingGridContainer.m_7407_(i, 1);
            slotStack = this.craftingGridContainer.m_8020_(i);
         }

         if (!recipeStack.m_41619_()) {
            if (slotStack.m_41619_()) {
               this.craftingGridContainer.m_6836_(i, recipeStack);
            } else if (ItemStack.m_41746_(slotStack, recipeStack) && ItemStack.m_41658_(slotStack, recipeStack)) {
               recipeStack.m_41769_(slotStack.m_41613_());
               this.craftingGridContainer.m_6836_(i, recipeStack);
            } else if (!this.player.m_150109_().m_36054_(recipeStack)) {
               this.player.m_36176_(recipeStack, false);
            }
         }
      }

   }

   public boolean m_6875_(Player player) {
      return player.m_6084_();
   }

   @Nullable
   private ManasSkillInstance getSkillInstance(Player player) {
      Optional<ManasSkillInstance> optional = SkillAPI.getSkillsFrom(player).getSkill(this.skill);
      return (ManasSkillInstance)optional.orElse((Object)null);
   }

   public void m_6877_(Player pPlayer) {
      ManasSkillInstance instance = this.getSkillInstance(pPlayer);
      if (instance != null) {
         ManasSkill var4 = instance.getSkill();
         if (var4 instanceof ISpatialStorage) {
            ISpatialStorage spatialStorage = (ISpatialStorage)var4;
            spatialStorage.saveContainer(instance, pPlayer, this.container);
         }
      }

      this.container.m_5785_(pPlayer);
      this.m_150411_(pPlayer, this.craftingGridContainer);
      this.m_150411_(pPlayer, this.furnaceInputContainer);
      this.m_150411_(pPlayer, this.furnaceResultContainer);
      this.craftingResultContainer.m_6211_();
      super.m_6877_(pPlayer);
   }

   public ItemStack m_7648_(Player pPlayer, int pIndex) {
      ItemStack copy = ItemStack.f_41583_;
      Slot slot = (Slot)this.f_38839_.get(pIndex);
      if (!slot.m_6657_()) {
         return copy;
      } else {
         ItemStack stack = slot.m_7993_();
         copy = stack.m_41777_();
         if (pIndex < 52) {
            if (!this.m_38903_(stack, 52, 88, true)) {
               return ItemStack.f_41583_;
            }
         } else if (!this.m_38903_(stack, 0, 52, false)) {
            return ItemStack.f_41583_;
         }

         if (stack.m_41619_()) {
            slot.m_5852_(ItemStack.f_41583_);
         } else {
            slot.m_6654_();
         }

         slot.m_142406_(pPlayer, stack);
         if (slot.f_40219_ == 51) {
            this.itemCrafted(stack);
            this.slotChangedCraftingGrid();
         }

         return copy;
      }
   }

   protected boolean m_38903_(ItemStack pStack, int pStartIndex, int pEndIndex, boolean pReverseDirection) {
      boolean flag = false;
      int i = pStartIndex;
      if (pReverseDirection) {
         i = pEndIndex - 1;
      }

      Slot pSlot;
      ItemStack itemstack;
      if (pStack.m_41753_()) {
         while(!pStack.m_41619_()) {
            if (pReverseDirection) {
               if (i < pStartIndex) {
                  break;
               }
            } else if (i >= pEndIndex) {
               break;
            }

            pSlot = (Slot)this.f_38839_.get(i);
            itemstack = pSlot.m_7993_();
            if (!itemstack.m_41619_() && pSlot.m_5857_(pStack) && ItemStack.m_150942_(pStack, itemstack)) {
               int j = itemstack.m_41613_() + pStack.m_41613_();
               int maxSize = i < 42 ? this.container.m_6893_() : Math.min(itemstack.m_41741_(), pSlot.m_6641_());
               if (j <= maxSize) {
                  pStack.m_41764_(0);
                  itemstack.m_41764_(j);
                  pSlot.m_6654_();
                  flag = true;
               } else if (itemstack.m_41613_() < maxSize) {
                  pStack.m_41774_(maxSize - itemstack.m_41613_());
                  itemstack.m_41764_(maxSize);
                  pSlot.m_6654_();
                  flag = true;
               }
            }

            if (pReverseDirection) {
               --i;
            } else {
               ++i;
            }
         }
      }

      if (!pStack.m_41619_()) {
         if (pReverseDirection) {
            i = pEndIndex - 1;
         } else {
            i = pStartIndex;
         }

         while(true) {
            if (pReverseDirection) {
               if (i < pStartIndex) {
                  break;
               }
            } else if (i >= pEndIndex) {
               break;
            }

            pSlot = (Slot)this.f_38839_.get(i);
            itemstack = pSlot.m_7993_();
            if (itemstack.m_41619_() && pSlot.m_5857_(pStack)) {
               if (pStack.m_41613_() > pSlot.m_6641_()) {
                  pSlot.m_5852_(pStack.m_41620_(pSlot.m_6641_()));
               } else {
                  pSlot.m_5852_(pStack.m_41620_(pStack.m_41613_()));
               }

               pSlot.m_6654_();
               flag = true;
               break;
            }

            if (pReverseDirection) {
               --i;
            } else {
               ++i;
            }
         }
      }

      return flag;
   }

   public ManasSkill getSkill() {
      return this.skill;
   }

   public ContainerLevelAccess getAccess() {
      return this.access;
   }

   public Player getPlayer() {
      return this.player;
   }
}
