package com.github.manasmods.tensura.ability.skill.common;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.entity.template.TensuraHorseEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import java.util.Iterator;
import java.util.List;
import net.minecraft.ChatFormatting;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.player.Player;

public class ThoughtCommunicationSkill extends Skill {
   public ThoughtCommunicationSkill() {
      super(Skill.SkillType.COMMON);
   }

   protected boolean canActivateInRaceLimit(ManasSkillInstance instance) {
      return true;
   }

   public int modes() {
      return 2;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      if (instance.isMastered(entity)) {
         return instance.getMode() == 1 ? 2 : 1;
      } else {
         return instance.getMode() == 1 ? 0 : 1;
      }
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.thought_communication.movement");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.thought_communication.targeting");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (instance.getMode() == 1) {
         movementBehaviour(instance, entity);
      } else {
         targetingBehaviour(instance, entity);
      }

   }

   public static void movementBehaviour(ManasSkillInstance instance, LivingEntity entity) {
      CompoundTag tag = instance.getOrCreateTag();
      if (entity.m_6144_()) {
         List<Mob> list = entity.m_9236_().m_6443_(Mob.class, entity.m_20191_().m_82400_(15.0D), (living) -> {
            return living.m_7307_(entity) && !living.m_7306_(entity);
         });
         if (list.isEmpty()) {
            if (entity instanceof Player) {
               Player player = (Player)entity;
               player.m_5661_(Component.m_237115_("tensura.telepathy.subordinate_all.not_found").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.AQUA)), true);
            }

            return;
         }

         if (tag.m_128451_("usedTimes") % 10 == 0) {
            instance.addMasteryPoint(entity);
         }

         tag.m_128405_("usedTimes", tag.m_128451_("usedTimes") + 1);
         int command = tag.m_128451_("command");
         command = command == 3 ? 1 : command + 1;
         tag.m_128405_("command", command);
         instance.markDirty();

         for(Iterator var5 = list.iterator(); var5.hasNext(); entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12317_, SoundSource.PLAYERS, 1.0F, 1.0F)) {
            Mob mob = (Mob)var5.next();
            MutableComponent message;
            switch(command) {
            case 2:
               SkillHelper.setFollow(mob);
               message = Component.m_237115_("tensura.telepathy.subordinate_all.follow");
               break;
            case 3:
               SkillHelper.setWander(mob);
               message = Component.m_237115_("tensura.telepathy.subordinate_all.wander");
               break;
            default:
               SkillHelper.setStay(mob);
               message = Component.m_237115_("tensura.telepathy.subordinate_all.stay");
            }

            if (entity instanceof Player) {
               Player player = (Player)entity;
               player.m_5661_(message.m_6270_(Style.f_131099_.m_131140_(ChatFormatting.AQUA)), true);
            }
         }
      } else {
         movementTelepathy(instance, entity);
      }

   }

   public static void movementTelepathy(ManasSkillInstance instance, LivingEntity entity) {
      CompoundTag tag = instance.getOrCreateTag();
      LivingEntity target = SkillHelper.getTargetingEntity(entity, 30.0D, false, false);
      if (target == null) {
         if (entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237115_("tensura.targeting.not_targeted").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.AQUA)), true);
         }

      } else {
         if (SkillHelper.isSubordinate(entity, target) && target instanceof Mob) {
            Mob mob = (Mob)target;
            TelepathySkill.telepathy(instance, entity, mob);
         } else if (attackCommand(entity, entity, target, 30.0D)) {
            if (tag.m_128451_("usedTimes") % 10 == 0) {
               instance.addMasteryPoint(entity);
            }

            tag.m_128405_("usedTimes", tag.m_128451_("usedTimes") + 1);
            instance.markDirty();
         }

      }
   }

   public static boolean attackCommand(Entity directCommander, LivingEntity owner, LivingEntity target, double radius) {
      if (target instanceof Player) {
         Player pPlayer = (Player)target;
         if (pPlayer.m_7500_() || pPlayer.m_5833_()) {
            return false;
         }
      }

      List<Mob> list = directCommander.m_9236_().m_6443_(Mob.class, directCommander.m_20191_().m_82400_(radius), (mobx) -> {
         return SkillHelper.isSubordinate(owner, mobx);
      });
      if (list.isEmpty()) {
         if (directCommander == owner && owner instanceof Player) {
            Player player = (Player)owner;
            player.m_5661_(Component.m_237115_("tensura.telepathy.subordinate_all.not_found").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.AQUA)), true);
         }

         return false;
      } else {
         Iterator var6 = list.iterator();

         while(var6.hasNext()) {
            Mob mob = (Mob)var6.next();
            mob.m_6710_(target);
            SkillHelper.setFollow(mob);
         }

         if (directCommander == owner) {
            owner.m_21011_(InteractionHand.MAIN_HAND, true);
         }

         directCommander.m_9236_().m_6263_((Player)null, directCommander.m_20185_(), directCommander.m_20186_(), directCommander.m_20189_(), SoundEvents.f_12317_, SoundSource.PLAYERS, 1.0F, 1.0F);
         return true;
      }
   }

   public static void targetingBehaviour(ManasSkillInstance instance, LivingEntity entity) {
      CompoundTag tag = instance.getOrCreateTag();
      if (entity.m_6144_()) {
         List<Mob> list = entity.m_9236_().m_6443_(Mob.class, entity.m_20191_().m_82400_(15.0D), (living) -> {
            return living.m_7307_(entity) && !living.m_7306_(entity);
         });
         if (list.isEmpty()) {
            if (entity instanceof Player) {
               Player player = (Player)entity;
               player.m_5661_(Component.m_237115_("tensura.telepathy.subordinate_all.not_found").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.AQUA)), true);
            }

            return;
         }

         int command = tag.m_128451_("targetingCommand");
         command = command == 3 ? 0 : command + 1;
         tag.m_128405_("targetingCommand", command);
         instance.markDirty();

         for(Iterator var5 = list.iterator(); var5.hasNext(); entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12317_, SoundSource.PLAYERS, 1.0F, 1.0F)) {
            Mob mob = (Mob)var5.next();
            MutableComponent message;
            switch(command) {
            case 1:
               SkillHelper.setAggressive(mob);
               message = Component.m_237115_("tensura.telepathy.subordinate_all.aggressive");
               break;
            case 2:
               SkillHelper.setProtect(mob);
               message = Component.m_237115_("tensura.telepathy.subordinate_all.protect");
               break;
            case 3:
               SkillHelper.setNeutral(mob);
               message = Component.m_237115_("tensura.telepathy.subordinate_all.neutral");
               break;
            default:
               SkillHelper.setPassive(mob);
               message = Component.m_237115_("tensura.telepathy.subordinate_all.passive");
            }

            if (entity instanceof Player) {
               Player player = (Player)entity;
               player.m_5661_(message.m_6270_(Style.f_131099_.m_131140_(ChatFormatting.AQUA)), true);
            }
         }
      } else {
         targetingTelepathy(entity);
      }

   }

   public static void targetingTelepathy(LivingEntity entity) {
      Mob mob = (Mob)SkillHelper.getTargetingEntity(Mob.class, entity, 30.0D, 0.2D, false, false);
      if (mob != null && SkillHelper.isSubordinate(entity, mob)) {
         if (mob instanceof TensuraTamableEntity) {
            TensuraTamableEntity tamable = (TensuraTamableEntity)mob;
            tamable.cycleBehaviour(entity);
         } else if (mob instanceof TensuraHorseEntity) {
            TensuraHorseEntity horse = (TensuraHorseEntity)mob;
            horse.cycleBehaviour(entity);
         }

         entity.m_21011_(InteractionHand.MAIN_HAND, true);
         entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12317_, SoundSource.PLAYERS, 1.0F, 1.0F);
      } else if (entity instanceof Player) {
         Player player = (Player)entity;
         player.m_5661_(Component.m_237115_("tensura.telepathy.subordinate.not_found").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
      }

   }
}
