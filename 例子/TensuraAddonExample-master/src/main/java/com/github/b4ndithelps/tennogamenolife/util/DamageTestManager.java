package com.github.b4ndithelps.tennogamenolife.util;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 管理玩家的伤害测试模式状态
 */
public class DamageTestManager {
    private static final Map<UUID, Boolean> playerDamageTestModes = new HashMap<>();
    
    /**
     * 设置玩家的伤害测试模式
     * 
     * @param playerUUID 玩家UUID
     * @param enabled 是否启用
     */
    public static void setDamageTestMode(UUID playerUUID, boolean enabled) {
        playerDamageTestModes.put(playerUUID, enabled);
    }
    
    /**
     * 获取玩家的伤害测试模式状态
     * 
     * @param playerUUID 玩家UUID
     * @return 是否启用伤害测试模式
     */
    public static boolean isDamageTestModeEnabled(UUID playerUUID) {
        return playerDamageTestModes.getOrDefault(playerUUID, false);
    }
} 