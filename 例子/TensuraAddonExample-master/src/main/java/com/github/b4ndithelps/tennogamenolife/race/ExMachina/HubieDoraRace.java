package com.github.b4ndithelps.tennogamenolife.race.ExMachina;

import com.github.b4ndithelps.tennogamenolife.registry.item.Tenreincarnationitems;
import com.github.b4ndithelps.tennogamenolife.registry.race.Tenreincarnationraces;
import com.github.b4ndithelps.tennogamenolife.registry.skill.TenreincarnationSkill;
import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.github.manasmods.tensura.util.JumpPowerHelper;
import com.mojang.datafixers.util.Pair;
import net.minecraft.network.chat.Component;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.entity.player.Player;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.client.player.LocalPlayer;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.stats.Stats;

import java.util.ArrayList;
import java.util.List;

/**
 * 休比·多拉是解析体的进化形态
 */
public class HubieDoraRace extends Race {

    // 种族基础属性
    private double baseHealth = 520.0;          // 基础生命值
    private double baseAttackDamage = 3.5;     // 基础攻击伤害
    private double baseAttackSpeed = 4.7;      // 基础攻击速度
    private double knockbackResistance = 0.6;   // 击退抗性
    private double jumpHeight = 1.1;           // 跳跃高度
    private double movementSpeed = 0.12;        // 移动速度
    private double sprintSpeed = 0.16;         // 冲刺速度
    private double auraMin = 3000.0;           // 最小灵气值
    private double auraMax = 4000.0;           // 最大灵气值
    private double startingMagiculeMin = 500.0; // 最小初始魔素值
    private double startingMagiculeMax = 600.0; // 最大初始魔素值

    private float playerSize = 2.0f;           // 玩家大小

    public HubieDoraRace() {
        // 设置为困难种族
        super(Difficulty.HARD);
    }

    @Override
    public double getBaseHealth() {
        return baseHealth;
    }

    @Override
    public float getPlayerSize() {
        return playerSize;
    }

    @Override
    public double getBaseAttackDamage() {
        return baseAttackDamage;
    }

    @Override
    public double getBaseAttackSpeed() {
        return baseAttackSpeed;
    }

    @Override
    public double getKnockbackResistance() {
        return knockbackResistance;
    }

    @Override
    public double getJumpHeight() {
        return JumpPowerHelper.defaultPlayer(jumpHeight);
    }

    @Override
    public double getMovementSpeed() {
        return movementSpeed;
    }

    @Override
    public double getSprintSpeed() {
        return sprintSpeed;
    }

    @Override
    public Pair<Double, Double> getBaseAuraRange() {
        return Pair.of(auraMin, auraMax);
    }

    @Override
    public Pair<Double, Double> getBaseMagiculeRange() {
        return Pair.of(startingMagiculeMin, startingMagiculeMax);
    }

    @Override
    public List<TensuraSkill> getIntrinsicSkills(Player player) {
        List<TensuraSkill> list = new ArrayList<>();
        list.add(IntrinsicSkills.BODY_ARMOR.get());
        list.add(ExtraSkills.HEAVENLY_EYE.get());
        list.add(ExtraSkills.MAGIC_SENSE.get());
        list.add(ExtraSkills.THOUGHT_ACCELERATION.get());
        list.add(ExtraSkills.ULTRASPEED_REGENERATION.get());
        list.add(ResistanceSkills.COLD_RESISTANCE.get());
        list.add(ResistanceSkills.MAGIC_RESISTANCE.get());
        list.add(ResistanceSkills.HEAT_RESISTANCE.get());
        list.add(ResistanceSkills.PHYSICAL_ATTACK_RESISTANCE.get());
        return list;
    }

    @Override
    public boolean isMajin() {
        return false;
    }

    @Override
    public boolean isSpiritual() {
        return false;
    }

    @Override
    public boolean isDivine() {
        return false;
    }

    @Override
    public void raceAbility(Player player) {
        if (!player.isSpectator() && !player.isCreative()) {
            if (player.getAbilities().mayfly) {
                // 关闭飞行
                player.getAbilities().mayfly = false;
                player.getAbilities().flying = false;
                player.onUpdateAbilities();
            } else {
                // 开启飞行
                player.getAbilities().mayfly = true;
                player.getAbilities().flying = true;
                player.onUpdateAbilities();
            }
        }
    }

    @Override
    public double getEvolutionPercentage(Player player) {
        // 限制EP进度最多只能达到50%
        double epPercentage = Math.min(50.0, TensuraPlayerCapability.getBaseEP(player) * 100.0 / 400000.0);
        
        // 如果EP进度达到50%，计算机械核心的使用次数
        if (epPercentage >= 50.0) {
            double coreCount = 0.0;
            if (player instanceof LocalPlayer) {
                LocalPlayer localPlayer = (LocalPlayer)player;
                coreCount = (double)localPlayer.getStats().getValue(Stats.ITEM_USED.get(Tenreincarnationitems.MECHANICAL_CORE.get()));
            } else if (player instanceof ServerPlayer) {
                ServerPlayer serverPlayer = (ServerPlayer)player;
                coreCount = (double)serverPlayer.getStats().getValue(Stats.ITEM_USED.get(Tenreincarnationitems.MECHANICAL_CORE.get()));
            }
            
            // 每个机械核心增加10%进度
            double corePercentage = coreCount * 10.0;
            
            // 返回EP进度和机械核心进度的总和，但不超过100%
            return Math.min(100.0, epPercentage + corePercentage);
        }
        
        return epPercentage;
    }

    @Override
    public List<Component> getRequirementsForRendering(Player player) {
        List<Component> list = new ArrayList<>();
        list.add(Component.translatable("tensura.evolution_menu.ep_requirement"));
        
        // 如果EP进度达到50%以上，显示机械核心要求
        double epPercentage = TensuraPlayerCapability.getBaseEP(player) * 100.0 / (Double)TensuraConfig.INSTANCE.racesConfig.epToEnlightened.get();
        if (epPercentage >= 50.0) {
            list.add(Component.translatable("tensura.evolution_menu.consume_requirement", 
                Tenreincarnationitems.MECHANICAL_CORE.get().getDescription().getString()));
        }
        
        return list;
    }

    @Override
    public List<Race> getPreviousEvolutions(Player player) {
        List<Race> list = new ArrayList<>();
        list.add(Tenreincarnationraces.PRUEFER.get());
        return list;
    }

    @Override
    public List<Race> getNextEvolutions(Player player) {
        List<Race> list = new ArrayList<>();
        list.add(Tenreincarnationraces.HOROU.get());
        list.add(Tenreincarnationraces.PURAIYA.get());
        return list;
    }

    @Override
    public Race getDefaultEvolution(Player player) {
        return Tenreincarnationraces.HOROU.get();
    }
} 