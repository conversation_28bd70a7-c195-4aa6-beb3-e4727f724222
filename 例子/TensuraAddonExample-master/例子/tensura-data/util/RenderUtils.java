package com.github.manasmods.tensura.util;

import com.github.manasmods.tensura.data.pack.KilnMoltenMaterial;
import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.BufferBuilder;
import com.mojang.blaze3d.vertex.BufferUploader;
import com.mojang.blaze3d.vertex.DefaultVertexFormat;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.Tesselator;
import com.mojang.blaze3d.vertex.VertexFormat.Mode;
import com.mojang.math.Matrix4f;
import java.awt.Color;
import net.minecraft.client.renderer.GameRenderer;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.resources.ResourceLocation;

public final class RenderUtils {
   private static final ResourceLocation FLUID = new ResourceLocation("tensura", "textures/gui/kiln/molten.png");

   public static void renderMoltenMaterial(PoseStack stack, KilnMoltenMaterial moltenMaterial, int progress, int maxProgress) {
      int width = true;
      int height = progress * 74 / maxProgress;
      if (height < 1) {
         height = 1;
      }

      int renderX = moltenMaterial.isRightBar() ? 145 : 18;
      int renderY = 80 - height;
      float u = 1.0F;
      float v = 0.013513514F * (float)height;
      Matrix4f pMatrix = stack.m_85850_().m_85861_();
      BufferBuilder bufferbuilder = Tesselator.m_85913_().m_85915_();
      RenderSystem.m_69478_();
      RenderSystem.m_69453_();
      RenderSystem.m_157456_(0, FLUID);
      RenderSystem.m_157427_(GameRenderer::m_172814_);
      bufferbuilder.m_166779_(Mode.QUADS, DefaultVertexFormat.f_85818_);
      bufferbuilder.m_85982_(pMatrix, (float)renderX, (float)renderY + (float)height, 0.0F).m_6122_(moltenMaterial.getRed(), moltenMaterial.getGreen(), moltenMaterial.getBlue(), moltenMaterial.getAlpha()).m_7421_(0.0F, v).m_5752_();
      bufferbuilder.m_85982_(pMatrix, (float)renderX + 13.0F, (float)renderY + (float)height, 0.0F).m_6122_(moltenMaterial.getRed(), moltenMaterial.getGreen(), moltenMaterial.getBlue(), moltenMaterial.getAlpha()).m_7421_(1.0F, v).m_5752_();
      bufferbuilder.m_85982_(pMatrix, (float)renderX + 13.0F, (float)renderY, 0.0F).m_6122_(moltenMaterial.getRed(), moltenMaterial.getGreen(), moltenMaterial.getBlue(), moltenMaterial.getAlpha()).m_7421_(1.0F, 0.0F).m_5752_();
      bufferbuilder.m_85982_(pMatrix, (float)renderX, (float)renderY, 0.0F).m_6122_(moltenMaterial.getRed(), moltenMaterial.getGreen(), moltenMaterial.getBlue(), moltenMaterial.getAlpha()).m_7421_(0.0F, 0.0F).m_5752_();
      BufferUploader.m_231202_(bufferbuilder.m_231175_());
      RenderSystem.m_69461_();
   }

   public static MutableComponent toolTipFromMoltenMaterial(KilnMoltenMaterial moltenMaterial, float amount, int maxAmount) {
      MutableComponent moltenMaterialName = Component.m_237115_(String.format("%s.molten.%s.material", moltenMaterial.getMoltenType().m_135827_(), moltenMaterial.getMoltenType().m_135815_()));
      int textColor = (new Color(moltenMaterial.getRed(), moltenMaterial.getGreen(), moltenMaterial.getBlue())).getRGB();
      String moltenAmount = amount + "/" + maxAmount;
      return Component.m_237110_("tooltip.tensura.kiln.molten_item", new Object[]{moltenAmount, moltenMaterialName}).m_130948_(Style.f_131099_.m_178520_(textColor));
   }

   private RenderUtils() {
      throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
   }
}
