{"type": "minecraft:chest", "pools": [{"bonus_rolls": 0.0, "entries": [{"type": "minecraft:item", "name": "minecraft:cooked_cod"}], "rolls": {"type": "minecraft:uniform", "max": 8.0, "min": 1.0}}, {"bonus_rolls": 0.0, "entries": [{"type": "minecraft:item", "name": "minecraft:cooked_salmon"}], "rolls": {"type": "minecraft:uniform", "max": 8.0, "min": 1.0}}, {"bonus_rolls": 0.0, "entries": [{"type": "minecraft:item", "name": "minecraft:iron_helmet", "weight": 10}, {"type": "minecraft:item", "name": "minecraft:iron_chestplate", "weight": 10}, {"type": "minecraft:item", "name": "minecraft:iron_leggings", "weight": 10}, {"type": "minecraft:item", "name": "minecraft:iron_boots", "weight": 10}, {"type": "minecraft:item", "name": "minecraft:air", "weight": 60}], "rolls": 1.0}, {"bonus_rolls": 0.0, "entries": [{"type": "minecraft:item", "name": "tensura:monster_leather_d", "weight": 20}, {"type": "minecraft:item", "name": "minecraft:air", "weight": 80}], "rolls": {"type": "minecraft:uniform", "max": 3.0, "min": 1.0}}, {"bonus_rolls": 0.0, "entries": [{"type": "minecraft:item", "name": "minecraft:seagrass", "weight": 25}, {"type": "minecraft:item", "name": "minecraft:air", "weight": 75}], "rolls": {"type": "minecraft:uniform", "max": 5.0, "min": 1.0}}, {"bonus_rolls": 0.0, "entries": [{"type": "minecraft:item", "name": "minecraft:sponge", "weight": 30}, {"type": "minecraft:item", "name": "minecraft:air", "weight": 70}], "rolls": {"type": "minecraft:uniform", "max": 2.0, "min": 1.0}}]}