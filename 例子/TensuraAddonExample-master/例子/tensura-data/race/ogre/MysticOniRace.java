package com.github.manasmods.tensura.race.ogre;

import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.registry.items.TensuraMobDropItems;
import com.github.manasmods.tensura.registry.race.TensuraRaces;
import com.github.manasmods.tensura.util.JumpPowerHelper;
import com.mojang.datafixers.util.Pair;
import java.util.ArrayList;
import java.util.List;
import net.minecraft.client.player.LocalPlayer;
import net.minecraft.network.chat.Component;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.stats.Stats;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import org.jetbrains.annotations.Nullable;

public class MysticOniRace extends EnlightenedOgreRace {
   public double getBaseHealth() {
      return 150.0D;
   }

   public double getBaseAttackSpeed() {
      return 4.6D;
   }

   public double getKnockbackResistance() {
      return 0.4D;
   }

   public double getJumpHeight() {
      return JumpPowerHelper.defaultPlayer(1.0D);
   }

   public double getSprintSpeed() {
      return 0.5D;
   }

   public Pair<Double, Double> getBaseAuraRange() {
      return Pair.of(40000.0D, 100000.0D);
   }

   public Pair<Double, Double> getBaseMagiculeRange() {
      return Pair.of(40000.0D, 100000.0D);
   }

   public double getAdditionalSpiritualHealth() {
      return 40.0D;
   }

   public double getSpiritualHealthMultiplier() {
      return 3.0D;
   }

   @Nullable
   public Race getDefaultEvolution(Player player) {
      return (Race)TensuraRaces.SPIRIT_ONI.get();
   }

   @Nullable
   public Race getHarvestFestivalEvolution(Player player) {
      return null;
   }

   public List<Race> getNextEvolutions(Player player) {
      List<Race> list = new ArrayList();
      list.add((Race)TensuraRaces.SPIRIT_ONI.get());
      return list;
   }

   public List<Race> getPreviousEvolutions(Player player) {
      List<Race> list = new ArrayList();
      list.add((Race)TensuraRaces.KIJIN.get());
      list.add((Race)TensuraRaces.ENLIGHTENED_OGRE.get());
      return list;
   }

   public double getAuraEvolutionReward() {
      return 40000.0D;
   }

   public double getManaEvolutionReward() {
      return 40000.0D;
   }

   public double getEvolutionPercentage(Player player) {
      double essence = 0.0D;
      if (player instanceof LocalPlayer) {
         LocalPlayer localPlayer = (LocalPlayer)player;
         essence = (double)localPlayer.m_108630_().m_13015_(Stats.f_12982_.m_12902_((Item)TensuraMobDropItems.ELEMENTAL_ESSENCE.get()));
      } else if (player instanceof ServerPlayer) {
         ServerPlayer serverPlayer = (ServerPlayer)player;
         essence = (double)serverPlayer.m_8951_().m_13015_(Stats.f_12982_.m_12902_((Item)TensuraMobDropItems.ELEMENTAL_ESSENCE.get()));
      }

      return essence * 100.0D / (double)(Integer)TensuraConfig.INSTANCE.racesConfig.essenceForMystic.get();
   }

   public List<Component> getRequirementsForRendering(Player player) {
      List<Component> list = new ArrayList();
      list.add(Component.m_237110_("tensura.evolution_menu.consume_requirement", new Object[]{((Item)TensuraMobDropItems.ELEMENTAL_ESSENCE.get()).m_7968_().m_41611_()}));
      return list;
   }
}
