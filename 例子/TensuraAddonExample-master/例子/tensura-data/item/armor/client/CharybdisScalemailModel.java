package com.github.manasmods.tensura.item.armor.client;

import com.github.manasmods.tensura.item.armor.CharybdisScalemailItem;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.model.AnimatedGeoModel;

public class CharybdisScalemailModel extends AnimatedGeoModel<CharybdisScalemailItem> {
   public ResourceLocation getModelResource(CharybdisScalemailItem object) {
      return new ResourceLocation("tensura", "geo/armor/charybdis_scalemail.geo.json");
   }

   public ResourceLocation getTextureResource(CharybdisScalemailItem object) {
      return new ResourceLocation("tensura", "textures/models/armor/charybdis_scalemail_layer_0.png");
   }

   public ResourceLocation getAnimationResource(CharybdisScalemailItem maskItem) {
      return new ResourceLocation("tensura", "animations/armor.animation.json");
   }
}
