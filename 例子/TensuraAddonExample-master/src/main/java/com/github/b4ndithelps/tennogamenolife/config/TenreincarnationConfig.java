package com.github.b4ndithelps.tennogamenolife.config;

import com.github.b4ndithelps.tennogamenolife.Tenreincarnation;
import net.minecraftforge.common.ForgeConfigSpec;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.event.config.ModConfigEvent;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

@Mod.EventBusSubscriber(modid = Tenreincarnation.MODID, bus = Mod.EventBusSubscriber.Bus.MOD)
public class TenreincarnationConfig {
    private static final Logger LOGGER = LogManager.getLogger(TenreincarnationConfig.class);

    public static final ForgeConfigSpec.Builder COMMON_BUILDER = new ForgeConfigSpec.Builder();
    public static final ForgeConfigSpec COMMON_SPEC;
    public static final CommonConfig COMMON;

    static {
        final Pair<CommonConfig, ForgeConfigSpec> pair = new ForgeConfigSpec.Builder().configure(CommonConfig::new);
        COMMON_SPEC = pair.getRight();
        COMMON = pair.getLeft();
    }

    public static class CommonConfig {
        // 技能相关配置
        public final ForgeConfigSpec.BooleanValue enableSkillSpecialization;
        public final ForgeConfigSpec.DoubleValue skillExperienceMultiplier;
        public final ForgeConfigSpec.IntValue maxSkillLevel;

        public CommonConfig(ForgeConfigSpec.Builder builder) {
            // 技能配置部分
            builder.comment("Skill Configuration").push("skills");
            
            enableSkillSpecialization = builder
                    .comment("Enable skill specialization feature")
                    .define("enableSkillSpecialization", true);
            
            skillExperienceMultiplier = builder
                    .comment("Multiplier for skill experience gain")
                    .defineInRange("skillExperienceMultiplier", 1.0, 0.1, 10.0);
            
            maxSkillLevel = builder
                    .comment("Maximum level for skills")
                    .defineInRange("maxSkillLevel", 100, 1, 1000);
            
            builder.pop();
        }
    }

    @SubscribeEvent
    public static void onModConfigEvent(final ModConfigEvent configEvent) {
        if (configEvent.getConfig().getSpec() == TenreincarnationConfig.COMMON_SPEC) {
            LOGGER.info("Loading TenNoGameNoLife config...");
        }
    }
} 