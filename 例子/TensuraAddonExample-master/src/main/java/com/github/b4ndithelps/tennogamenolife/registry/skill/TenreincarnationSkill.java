package com.github.b4ndithelps.tennogamenolife.registry.skill;

import com.github.b4ndithelps.tennogamenolife.Tenreincarnation;
import com.github.b4ndithelps.tennogamenolife.ability.skill.extra.BarrierSkill;
import com.github.b4ndithelps.tennogamenolife.ability.skill.unique.ChainMiningSkill;
import com.github.b4ndithelps.tennogamenolife.ability.skill.unique.ChainFellingSkill;
import com.github.b4ndithelps.tennogamenolife.ability.skill.unique.ChainShovelSkill;
import com.github.b4ndithelps.tennogamenolife.ability.skill.unique.MagicBeadsSkill;
import com.github.b4ndithelps.tennogamenolife.ability.skill.ultimate.TengekiUltimateSkill;
import com.github.b4ndithelps.tennogamenolife.ability.skill.ultimate.ReisenUltimateSkill;
import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.RegistryObject;

/**
 * 这个文件负责向官方Tensura模组注册所有技能。
 * 它使用一个基于ManasCore技能注册表的DeferredRegister。
 * 你创建的任何技能都必须注册到这个注册表中。
 *
 * 所有类型的技能都可以在这里注册。
 */
public class TenreincarnationSkill {

    // 这里就是那个deferred register,你不需要改变它
    public static DeferredRegister<ManasSkill> skillRegistry = DeferredRegister.create(SkillAPI.getSkillRegistryKey(), Tenreincarnation.MODID);

    public static void register(IEventBus modEventBus) {
        skillRegistry.register(modEventBus);
    }

    // 在这里注册技能。要添加新技能，只需复制RegistryObject并修改它以匹配你在../ability/skill/中定义的技能

    //   ================
    //   | Extra Skills |
    //   ================
    public static final RegistryObject<BarrierSkill> BARRIER =
            skillRegistry.register("barrier", BarrierSkill::new);

    //   =================
    //   | Unique Skills |
    //   =================
    public static final RegistryObject<MagicBeadsSkill> MAGIC_BEADS =
            skillRegistry.register("magic_beads", MagicBeadsSkill::new);
            
    // 注册连锁挖矿技能        
    public static final RegistryObject<ChainMiningSkill> CHAIN_MINING =
            skillRegistry.register("chain_mining", ChainMiningSkill::new);
            
    // 注册连锁砍伐技能
    public static final RegistryObject<ChainFellingSkill> CHAIN_FELLING =
            skillRegistry.register("chain_felling", ChainFellingSkill::new);
            
    // 注册连锁铲技能
    public static final RegistryObject<ChainShovelSkill> CHAIN_SHOVEL =
            skillRegistry.register("chain_shovel", ChainShovelSkill::new);

    //   =====================
    //   | Ultimate Skills |
    //   =====================
    public static final RegistryObject<TengekiUltimateSkill> TENGEKI =
            skillRegistry.register("tengeki", TengekiUltimateSkill::new);
            
    // 注册典开技能
    public static final RegistryObject<ReisenUltimateSkill> REISEN =
            skillRegistry.register("reisen", ReisenUltimateSkill::new);
}
