package com.github.manasmods.tensura.effect.skill;

import com.github.manasmods.tensura.effect.template.SkillMobEffect;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import net.minecraft.world.effect.MobEffectCategory;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeMap;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;

public class ReaperReconEffect extends SkillMobEffect {
   public ReaperReconEffect(MobEffectCategory pCategory, int pColor) {
      super(pCategory, pColor);
      this.m_19472_((Attribute)TensuraAttributeRegistry.SIZE.get(), "17d4dca8-fc25-465a-9c51-ac7095f4d457", -0.5D, Operation.MULTIPLY_BASE);
   }

   public double m_7048_(int pAmplifier, AttributeModifier pModifier) {
      return pModifier.m_22218_();
   }

   public void m_6385_(LivingEntity pLivingEntity, AttributeMap pAttributeMap, int pAmplifier) {
      this.addAttributeModifiersWithMinSize(pLivingEntity, pAttributeMap, pAmplifier);
   }
}
