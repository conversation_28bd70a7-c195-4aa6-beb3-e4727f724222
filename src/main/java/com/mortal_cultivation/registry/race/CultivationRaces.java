package com.mortal_cultivation.registry.race;

import com.mortal_cultivation.MortalCultivation;
import com.mortal_cultivation.race.literal_translation.SigilMasterRace;
import com.mortal_cultivation.race.literal_translation.TalismanApprenticeRace;
import com.mortal_cultivation.race.literal_translation.TalismanMasterRace;
import com.mortal_cultivation.race.demonic_cultivator.DemonicFoundationRace;
import com.mortal_cultivation.race.right_way.DaoBreakingRace;
import com.mortal_cultivation.race.right_way.FoundationEstablishmentRace;
import com.mortal_cultivation.race.right_way.GoldenCoreRace;
import com.mortal_cultivation.race.right_way.GreatAscensionRace;
import com.mortal_cultivation.race.right_way.HeavenlyDaoRace;
import com.mortal_cultivation.race.right_way.NascentSoulRace;
import com.mortal_cultivation.race.right_way.SpiritTransformationRace;
import com.mortal_cultivation.race.right_way.TribulationTranscendenceRace;
import com.mortal_cultivation.race.right_way.TrueImmortalRace;
import com.mortal_cultivation.race.right_way.UnityRace;
import com.mortal_cultivation.race.right_way.VoidRefinementRace;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.registry.race.TensuraRaces;
import net.minecraft.core.Registry;
import net.minecraft.resources.ResourceKey;
import net.minecraft.resources.ResourceLocation;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.RegistryObject;

/**
 * 修仙种族注册类 - 控制所有修仙境界的种族注册
 */
public class CultivationRaces {
    private static final ResourceKey<Registry<Race>> RACE_REGISTRY_KEY = ResourceKey.createRegistryKey(new ResourceLocation("tensura", "races"));
    public static final DeferredRegister<Race> RACES = DeferredRegister.create(RACE_REGISTRY_KEY, MortalCultivation.MODID);
 
    // 注册筑基种族 (Foundation Establishment)
    public static final RegistryObject<FoundationEstablishmentRace> FOUNDATION_ESTABLISHMENT_RACE = 
        RACES.register("foundation_establishment_race", FoundationEstablishmentRace::new);
        
    // 注册金丹种族 (Golden Core)
    public static final RegistryObject<GoldenCoreRace> GOLDEN_CORE_RACE = 
        RACES.register("golden_core_race", GoldenCoreRace::new);
        
    // 注册元婴种族 (Nascent Soul)
    public static final RegistryObject<NascentSoulRace> NASCENT_SOUL_RACE = 
        RACES.register("nascent_soul_race", NascentSoulRace::new);
        
    // 注册化神种族 (Spirit Transformation)
    public static final RegistryObject<SpiritTransformationRace> SPIRIT_TRANSFORMATION_RACE = 
        RACES.register("spirit_transformation_race", SpiritTransformationRace::new);
        
    // 注册炼虚种族 (Void Refinement)
    public static final RegistryObject<VoidRefinementRace> VOID_REFINEMENT_RACE = 
        RACES.register("void_refinement_race", VoidRefinementRace::new);
        
    // 注册合体种族 (Unity)
    public static final RegistryObject<UnityRace> UNITY_RACE =
        RACES.register("unity_race", UnityRace::new);

    // 注册大乘种族 (Great Ascension)
    public static final RegistryObject<GreatAscensionRace> GREAT_ASCENSION_RACE =
        RACES.register("great_ascension_race", GreatAscensionRace::new);

    // 注册渡劫种族 (Tribulation Transcendence)
    public static final RegistryObject<TribulationTranscendenceRace> TRIBULATION_TRANSCENDENCE_RACE =
        RACES.register("tribulation_transcendence_race", TribulationTranscendenceRace::new);

    // 注册真仙境种族 (True Immortal)
    public static final RegistryObject<TrueImmortalRace> TRUE_IMMORTAL_RACE =
        RACES.register("true_immortal_race", TrueImmortalRace::new);

    // 注册天道境种族 (Heavenly Dao)
    public static final RegistryObject<HeavenlyDaoRace> HEAVENLY_DAO_RACE =
        RACES.register("heavenly_dao_race", HeavenlyDaoRace::new);

    // 注册破道境种族 (Dao-Breaking)
    public static final RegistryObject<DaoBreakingRace> DAO_BREAKING_RACE =
        RACES.register("dao_breaking_race", DaoBreakingRace::new);

    // 注册符徒种族 (Talisman Apprentice)
    public static final RegistryObject<TalismanApprenticeRace> TALISMAN_APPRENTICE_RACE = 
        RACES.register("talisman_apprentice_race", TalismanApprenticeRace::new);
        
    // 注册符师种族 (Talisman Master)
    public static final RegistryObject<TalismanMasterRace> TALISMAN_MASTER_RACE = 
        RACES.register("talisman_master_race", TalismanMasterRace::new);
        
    // 注册符尊种族 (Sigil Master)
    public static final RegistryObject<SigilMasterRace> SIGIL_MASTER_RACE =
        RACES.register("sigil_master_race", SigilMasterRace::new);

    // 注册魔筑基期种族 (Demonic Foundation)
    public static final RegistryObject<DemonicFoundationRace> DEMONIC_FOUNDATION_RACE =
        RACES.register("demonic_foundation_race", DemonicFoundationRace::new);

    /**
     * 向总线注册所有修仙种族
     * @param modEventBus Forge模组事件总线
     */
    public static void register(IEventBus modEventBus) {
        RACES.register(modEventBus);
    }
}
