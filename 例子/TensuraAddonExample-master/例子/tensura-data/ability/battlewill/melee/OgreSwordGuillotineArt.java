package com.github.manasmods.tensura.ability.battlewill.melee;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.battlewill.Battewill;
import com.github.manasmods.tensura.registry.battlewill.ProjectileArts;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import net.minecraft.ChatFormatting;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;

public class OgreSwordGuillotineArt extends Battewill {
   public double learningCost() {
      return 200.0D;
   }

   public double auraCost(LivingEntity entity, ManasSkillInstance instance) {
      return 200.0D;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (!entity.m_21023_((MobEffect)TensuraMobEffects.OGRE_GUILLOTINE.get())) {
         if (!SkillHelper.outOfAura(entity, instance)) {
            this.addMasteryPoint(instance, entity);
            entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.OGRE_GUILLOTINE.get(), instance.isMastered(entity) ? 600 : 300, instance.isMastered(entity) ? 1 : 0, false, false, false));
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11887_, SoundSource.PLAYERS, 0.5F, 1.0F);
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
         }
      }
   }

   public void onSkillMastered(ManasSkillInstance instance, LivingEntity entity) {
      SkillStorage storage = SkillAPI.getSkillsFrom(entity);
      ManasSkill skill = (ManasSkill)ProjectileArts.OGRE_SWORD_CANNON.get();
      ManasSkillInstance manipulation = new TensuraSkillInstance(skill);
      manipulation.setMastery(-100);
      if (storage.learnSkill(manipulation) && entity instanceof Player) {
         Player player = (Player)entity;
         player.m_5661_(Component.m_237110_("tensura.skill.learn_available", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_GREEN)), false);
      }

   }
}
