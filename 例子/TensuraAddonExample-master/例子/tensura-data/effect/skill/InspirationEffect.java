package com.github.manasmods.tensura.effect.skill;

import com.github.manasmods.manascore.attribute.ManasCoreAttributes;
import com.github.manasmods.tensura.effect.template.SkillMobEffect;
import net.minecraft.world.effect.MobEffectCategory;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraftforge.common.ForgeMod;

public class InspirationEffect extends SkillMobEffect {
   protected static final String INSPIRATION = "29df7ebb-4adc-44eb-bcb5-b0659ee26712";

   public InspirationEffect(MobEffectCategory pCategory, int pColor) {
      super(pCategory, pColor);
      this.m_19472_(Attributes.f_22281_, "29df7ebb-4adc-44eb-bcb5-b0659ee26712", 0.3D, Operation.MULTIPLY_BASE);
      this.m_19472_(Attributes.f_22283_, "29df7ebb-4adc-44eb-bcb5-b0659ee26712", 0.3D, Operation.MULTIPLY_BASE);
      this.m_19472_(Attributes.f_22282_, "29df7ebb-4adc-44eb-bcb5-b0659ee26712", 0.3D, Operation.MULTIPLY_BASE);
      this.m_19472_(Attributes.f_22288_, "29df7ebb-4adc-44eb-bcb5-b0659ee26712", 0.3D, Operation.MULTIPLY_BASE);
      this.m_19472_(Attributes.f_22278_, "29df7ebb-4adc-44eb-bcb5-b0659ee26712", 0.3D, Operation.MULTIPLY_BASE);
      this.m_19472_(Attributes.f_22276_, "29df7ebb-4adc-44eb-bcb5-b0659ee26712", 0.3D, Operation.MULTIPLY_BASE);
      this.m_19472_(Attributes.f_22279_, "29df7ebb-4adc-44eb-bcb5-b0659ee26712", 0.3D, Operation.MULTIPLY_BASE);
      this.m_19472_((Attribute)ForgeMod.SWIM_SPEED.get(), "29df7ebb-4adc-44eb-bcb5-b0659ee26712", 0.3D, Operation.MULTIPLY_BASE);
      this.m_19472_((Attribute)ManasCoreAttributes.JUMP_POWER.get(), "29df7ebb-4adc-44eb-bcb5-b0659ee26712", 0.3D, Operation.MULTIPLY_BASE);
      this.m_19472_((Attribute)ManasCoreAttributes.CRIT_CHANCE.get(), "29df7ebb-4adc-44eb-bcb5-b0659ee26712", 30.0D, Operation.ADDITION);
   }

   public boolean m_6584_(int duration, int amplifier) {
      return false;
   }
}
