package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.SpearToroEntity;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.processor.IBone;
import software.bernie.geckolib3.model.AnimatedGeoModel;

public class SpearToroModel extends AnimatedGeoModel<SpearToroEntity> {
   public ResourceLocation getModelResource(SpearToroEntity object) {
      return new ResourceLocation("tensura", "geo/spear_toro.geo.json");
   }

   public ResourceLocation getTextureResource(SpearToroEntity instance) {
      return new ResourceLocation("tensura", "textures/entity/spear_toro/spear_toro.png");
   }

   public ResourceLocation getAnimationResource(SpearToroEntity entity) {
      return new ResourceLocation("tensura", "animations/spear_toro.animation.json");
   }

   public void setCustomAnimations(SpearToroEntity fish, int instanceId, AnimationEvent customPredicate) {
      super.setCustomAnimations(fish, instanceId, customPredicate);
      IBone chest = this.getAnimationProcessor().getBone("Chest");
      if (fish.isChested() == chest.isHidden()) {
         chest.setHidden(!fish.isChested());
      }

      IBone saddle = this.getAnimationProcessor().getBone("Saddle");
      if (fish.isSaddled() == saddle.isHidden()) {
         saddle.setHidden(!fish.isSaddled());
      }

      if (fish.isSaddled()) {
         IBone saddleHandle = this.getAnimationProcessor().getBone("SaddleStrings");
         if (fish.m_20160_() == saddleHandle.isHidden()) {
            saddleHandle.setHidden(!fish.m_20160_());
         }
      }

   }
}
