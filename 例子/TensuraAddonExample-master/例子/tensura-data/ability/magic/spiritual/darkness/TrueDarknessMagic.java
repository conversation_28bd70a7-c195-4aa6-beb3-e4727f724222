package com.github.manasmods.tensura.ability.magic.spiritual.darkness;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.entity.magic.barrier.DarkCubeEntity;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;

public class TrueDarknessMagic extends SpiritualMagic {
   public TrueDarknessMagic() {
      super(MagicElemental.DARKNESS, SpiritualMagic.SpiritLevel.GREATER);
   }

   public int defaultCast() {
      return 120;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 10000.0D;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      instance.getOrCreateTag().m_128405_("BarrierID", 0);
      instance.markDirty();
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (heldTicks == 0 && this.alreadyCasting(entity)) {
         return false;
      } else {
         int castTime = this.castingTime(instance, entity);
         Level level = entity.m_9236_();
         if (heldTicks >= castTime) {
            if (heldTicks == castTime + 1) {
               this.addMasteryPoint(instance, entity);
            }

            DarkCubeEntity.spawnTrueCube((EntityType)TensuraEntityTypes.DARK_CUBE.get(), 100.0F, 7.5F, 30, true, entity.m_20182_().m_82520_(0.0D, -1.0D, 0.0D), entity, instance, this.magiculeCost(entity, instance), 1000.0D, heldTicks);
            TensuraParticleHelper.addServerParticlesAroundSelf(entity, (ParticleOptions)TensuraParticles.DARK_RED_LIGHTNING_SPARK.get());
            level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12049_, SoundSource.PLAYERS, 1.0F, 1.0F);
            return heldTicks - castTime <= (instance.isMastered(entity) ? 400 : 200);
         } else {
            if (entity instanceof Player) {
               Player player = (Player)entity;
               this.addCastingParticle(instance, player, heldTicks);
            }

            return true;
         }
      }
   }
}
