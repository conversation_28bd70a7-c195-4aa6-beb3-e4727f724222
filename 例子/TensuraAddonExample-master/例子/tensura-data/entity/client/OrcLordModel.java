package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.OrcLordEntity;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.processor.IBone;
import software.bernie.geckolib3.model.AnimatedGeoModel;
import software.bernie.geckolib3.model.provider.data.EntityModelData;

public class OrcLordModel extends AnimatedGeoModel<OrcLordEntity> {
   public ResourceLocation getModelResource(OrcLordEntity object) {
      return new ResourceLocation("tensura", "geo/orc_lord.geo.json");
   }

   public ResourceLocation getTextureResource(OrcLordEntity instance) {
      return new ResourceLocation("tensura", "textures/entity/orc/orc_lord.png");
   }

   public ResourceLocation getAnimationResource(OrcLordEntity bear) {
      return new ResourceLocation("tensura", "animations/orc_lord.animation.json");
   }

   public void setCustomAnimations(OrcLordEntity bear, int instanceId, AnimationEvent customPredicate) {
      super.setCustomAnimations(bear, instanceId, customPredicate);
      EntityModelData extraData = (EntityModelData)customPredicate.getExtraDataOfType(EntityModelData.class).get(0);
      IBone head = this.getAnimationProcessor().getBone("head");
      if (head != null) {
         head.setRotationX(extraData.headPitch * 3.1415927F / 180.0F);
         head.setRotationY(extraData.netHeadYaw * 3.1415927F / 180.0F);
      }

   }
}
