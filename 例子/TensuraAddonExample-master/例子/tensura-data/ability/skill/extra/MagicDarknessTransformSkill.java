package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.LivingEntity;

public class MagicDarknessTransformSkill extends MagicElementalTransformSkill {
   protected MagicElemental getMagicElemental() {
      return MagicElemental.DARKNESS;
   }

   protected ManasSkill getElementalTransform() {
      return (ManasSkill)IntrinsicSkills.DARKNESS_TRANSFORM.get();
   }

   protected MobEffect getMagicElementalEffect() {
      return (MobEffect)TensuraMobEffects.MAGIC_DARKNESS.get();
   }

   protected void doVisualEffect(LivingEntity entity) {
      TensuraParticleHelper.spawnServerParticles(entity.f_19853_, (ParticleOptions)TensuraParticles.DARK_RED_LIGHTNING_SPARK.get(), entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), 55, 0.08D, 0.08D, 0.08D, 0.5D, true);
   }
}
