package com.github.b4ndithelps.tennogamenolife.race.ExMachina;

import com.github.b4ndithelps.tennogamenolife.registry.race.Tenreincarnationraces;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.github.manasmods.tensura.util.JumpPowerHelper;
import com.mojang.datafixers.util.Pair;
import net.minecraft.network.chat.Component;
import net.minecraft.world.entity.player.Player;
import net.minecraft.ChatFormatting;

import java.util.ArrayList;
import java.util.List;

/**
 * 遗志体Puraiyā是休比·多拉的进化形态
 */
public class PuraiyaRace extends HubieDoraRace {

    // 种族基础属性
    private double baseHealth = 3300.0;          // 基础生命值
    private double baseAttackDamage = 10.0;     // 基础攻击伤害
    private double baseAttackSpeed = 5.0;      // 基础攻击速度
    private double knockbackResistance = 0.7;   // 击退抗性
    private double jumpHeight = 1.2;           // 跳跃高度
    private double movementSpeed = 0.14;        // 移动速度
    private double sprintSpeed = 0.18;         // 冲刺速度
    private double auraMin = 4000.0;           // 最小灵气值
    private double auraMax = 8000.0;           // 最大灵气值
    private double startingMagiculeMin = 1500.0; // 最小初始魔素值
    private double startingMagiculeMax = 2800.0; // 最大初始魔素值

    private float playerSize = 2.0f;           // 玩家大小

    public PuraiyaRace() {
        super();
    }

    @Override
    public double getBaseHealth() {
        return baseHealth;
    }

    @Override
    public float getPlayerSize() {
        return playerSize;
    }

    @Override
    public double getBaseAttackDamage() {
        return baseAttackDamage;
    }

    @Override
    public double getBaseAttackSpeed() {
        return baseAttackSpeed;
    }

    @Override
    public double getKnockbackResistance() {
        return knockbackResistance;
    }

    @Override
    public double getJumpHeight() {
        return JumpPowerHelper.defaultPlayer(jumpHeight);
    }

    @Override
    public double getMovementSpeed() {
        return movementSpeed;
    }

    @Override
    public double getSprintSpeed() {
        return sprintSpeed;
    }

    @Override
    public Pair<Double, Double> getBaseAuraRange() {
        return Pair.of(auraMin, auraMax);
    }

    @Override
    public Pair<Double, Double> getBaseMagiculeRange() {
        return Pair.of(startingMagiculeMin, startingMagiculeMax);
    }

    @Override
    public double getEvolutionPercentage(Player player) {
        double evolutionPercentage = 0.0;
        
        if (TensuraEPCapability.isMajin(player) || TensuraPlayerCapability.isTrueHero(player)) {
            evolutionPercentage += 50.0;
        }
        
        double epPercentage = Math.min(50.0, TensuraPlayerCapability.getBaseEP(player) * 100.0 / 1000000.0);
        evolutionPercentage += epPercentage;
        
        return Math.min(100.0, evolutionPercentage);
    }

    @Override
    public List<Component> getRequirementsForRendering(Player player) {
        List<Component> list = new ArrayList<>();
        list.add(Component.translatable("tensura.evolution_menu.awaken_requirement", 
                Component.translatable("tensura.attribute.true_demon_lord.name").withStyle(ChatFormatting.DARK_PURPLE), 
                Component.translatable("tensura.attribute.true_hero.name").withStyle(ChatFormatting.GOLD)));

        list.add(Component.translatable("tensura.evolution_menu.ep_requirement", 1000000));
        return list;
    }

    @Override
    public List<TensuraSkill> getIntrinsicSkills(Player player) {
        List<TensuraSkill> list = new ArrayList<>();

        return list;
    }

    @Override
    public void raceAbility(Player player) {
        if (!player.isSpectator() && !player.isCreative()) {
            if (player.getAbilities().mayfly) {
                player.getAbilities().mayfly = false;
                player.getAbilities().flying = false;
                player.onUpdateAbilities();
            } else {
                player.getAbilities().mayfly = true;
                player.getAbilities().flying = true;
                player.onUpdateAbilities();
            }
        }
    }

    @Override
    public List<Race> getPreviousEvolutions(Player player) {
        List<Race> list = new ArrayList<>();
        return list;
    }

    @Override
    public List<Race> getNextEvolutions(Player player) {
        List<Race> list = new ArrayList<>();
        return list;
    }

    @Override
    public Race getDefaultEvolution(Player player) {
        return null;
    }
} 