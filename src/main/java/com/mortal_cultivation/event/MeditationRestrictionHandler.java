package com.mortal_cultivation.event;

import com.mortal_cultivation.MortalCultivation;
import net.minecraft.client.Minecraft;
import net.minecraft.client.player.AbstractClientPlayer;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;
import net.minecraftforge.event.entity.living.LivingEvent;
import net.minecraftforge.event.entity.player.PlayerEvent;
import net.minecraftforge.event.entity.player.PlayerInteractEvent;
import net.minecraftforge.eventbus.api.EventPriority;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.client.event.MovementInputUpdateEvent;

/**
 * 冥想限制处理器
 * 在冥想状态下禁止玩家的所有动作和移动
 */
@Mod.EventBusSubscriber(modid = MortalCultivation.MODID, bus = Mod.EventBusSubscriber.Bus.FORGE, value = Dist.CLIENT)
@OnlyIn(Dist.CLIENT)
public class MeditationRestrictionHandler {

    /**
     * 禁止冥想时的移动输入
     */
    @SubscribeEvent(priority = EventPriority.HIGHEST)
    public static void onMovementInput(MovementInputUpdateEvent event) {
        if (CultivationAnimationHandler.isMeditating()) {
            // 清除所有移动输入
            event.getInput().up = false;
            event.getInput().down = false;
            event.getInput().left = false;
            event.getInput().right = false;
            event.getInput().jumping = false;
            event.getInput().shiftKeyDown = false;
            
            // 设置移动向量为0
            event.getInput().forwardImpulse = 0.0f;
            event.getInput().leftImpulse = 0.0f;
        }
    }

    /**
     * 禁止冥想时的玩家移动
     */
    @SubscribeEvent(priority = EventPriority.HIGHEST)
    public static void onLivingUpdate(LivingEvent.LivingTickEvent event) {
        if (!(event.getEntity() instanceof AbstractClientPlayer)) {
            return;
        }
        
        AbstractClientPlayer player = (AbstractClientPlayer) event.getEntity();
        Minecraft mc = Minecraft.getInstance();
        
        // 只处理本地玩家
        if (player != mc.player) {
            return;
        }
        
        if (CultivationAnimationHandler.isMeditating()) {
            // 停止水平移动，但保留Y轴移动（用于悬浮控制）
            Vec3 currentMovement = player.getDeltaMovement();
            player.setDeltaMovement(0, currentMovement.y, 0);

            // 如果玩家在地面上且不是悬浮状态，则停止Y轴移动
            if (player.isOnGround() && !player.isNoGravity()) {
                player.setDeltaMovement(0, 0, 0);
            }

            // 重置移动状态
            player.setSprinting(false);
            player.setShiftKeyDown(false);
        }
    }

    /**
     * 禁止冥想时的交互
     */
    @SubscribeEvent(priority = EventPriority.HIGHEST)
    public static void onPlayerInteract(PlayerInteractEvent event) {
        if (!(event.getEntity() instanceof AbstractClientPlayer)) {
            return;
        }
        
        AbstractClientPlayer player = (AbstractClientPlayer) event.getEntity();
        Minecraft mc = Minecraft.getInstance();
        
        // 只处理本地玩家
        if (player != mc.player) {
            return;
        }
        
        if (CultivationAnimationHandler.isMeditating()) {
            // 取消所有交互事件
            event.setCanceled(true);
        }
    }

    /**
     * 禁止冥想时的右键交互
     */
    @SubscribeEvent(priority = EventPriority.HIGHEST)
    public static void onRightClickItem(PlayerInteractEvent.RightClickItem event) {
        if (!(event.getEntity() instanceof AbstractClientPlayer)) {
            return;
        }
        
        AbstractClientPlayer player = (AbstractClientPlayer) event.getEntity();
        Minecraft mc = Minecraft.getInstance();
        
        // 只处理本地玩家
        if (player != mc.player) {
            return;
        }
        
        if (CultivationAnimationHandler.isMeditating()) {
            event.setCanceled(true);
        }
    }

    /**
     * 禁止冥想时的右键方块
     */
    @SubscribeEvent(priority = EventPriority.HIGHEST)
    public static void onRightClickBlock(PlayerInteractEvent.RightClickBlock event) {
        if (!(event.getEntity() instanceof AbstractClientPlayer)) {
            return;
        }
        
        AbstractClientPlayer player = (AbstractClientPlayer) event.getEntity();
        Minecraft mc = Minecraft.getInstance();
        
        // 只处理本地玩家
        if (player != mc.player) {
            return;
        }
        
        if (CultivationAnimationHandler.isMeditating()) {
            event.setCanceled(true);
        }
    }

    /**
     * 禁止冥想时的左键点击
     */
    @SubscribeEvent(priority = EventPriority.HIGHEST)
    public static void onLeftClickBlock(PlayerInteractEvent.LeftClickBlock event) {
        if (!(event.getEntity() instanceof AbstractClientPlayer)) {
            return;
        }
        
        AbstractClientPlayer player = (AbstractClientPlayer) event.getEntity();
        Minecraft mc = Minecraft.getInstance();
        
        // 只处理本地玩家
        if (player != mc.player) {
            return;
        }
        
        if (CultivationAnimationHandler.isMeditating()) {
            event.setCanceled(true);
        }
    }
}
