package com.github.manasmods.tensura.block.client;

import com.github.manasmods.tensura.block.MagicEngineBlock;
import com.github.manasmods.tensura.block.entity.MagicEngineBlockEntity;
import com.github.manasmods.tensura.registry.items.TensuraMobDropItems;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.math.Vector3f;
import net.minecraft.client.Minecraft;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.block.model.ItemTransforms.TransformType;
import net.minecraft.client.renderer.blockentity.BlockEntityRenderer;
import net.minecraft.client.renderer.blockentity.BlockEntityRendererProvider.Context;
import net.minecraft.client.renderer.entity.ItemRenderer;
import net.minecraft.client.renderer.texture.OverlayTexture;
import net.minecraft.core.Direction;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.ItemLike;
import net.minecraft.world.phys.Vec3;

public class MagicEngineBlockEntityRenderer implements BlockEntityRenderer<MagicEngineBlockEntity> {
   public MagicEngineBlockEntityRenderer(Context context) {
   }

   public void render(MagicEngineBlockEntity entity, float pPartialTick, PoseStack pPoseStack, MultiBufferSource pBufferSource, int pPackedLight, int pPackedOverlay) {
      if ((Boolean)entity.m_58900_().m_61143_(MagicEngineBlock.ENABLED)) {
         ItemRenderer itemRenderer = Minecraft.m_91087_().m_91291_();
         ItemStack itemStack = new ItemStack((ItemLike)TensuraMobDropItems.HIGH_QUALITY_MAGIC_CRYSTAL.get());
         pPoseStack.m_85836_();
         Direction direction = (Direction)entity.m_58900_().m_61143_(MagicEngineBlock.FACING);
         Vec3 offset = this.getRotationOffset(direction);
         pPoseStack.m_85837_(offset.m_7096_(), offset.m_7098_(), offset.m_7094_());
         pPoseStack.m_85841_(0.5F, 0.5F, 0.5F);
         pPoseStack.m_85845_(direction.m_122406_());
         pPoseStack.m_85845_(Vector3f.f_122225_.m_122270_(((float)(entity.spin++) + pPartialTick) / 20.0F - 45.0F));
         itemRenderer.m_174269_(itemStack, TransformType.HEAD, 15728880, OverlayTexture.f_118083_, pPoseStack, pBufferSource, 1);
         pPoseStack.m_85849_();
      }
   }

   private Vec3 getRotationOffset(Direction direction) {
      Vec3 var10000;
      switch(direction) {
      case DOWN:
         var10000 = new Vec3(0.5D, -0.3D, 0.5D);
         break;
      case UP:
         var10000 = new Vec3(0.5D, 1.3D, 0.5D);
         break;
      case EAST:
         var10000 = new Vec3(1.3D, 0.5D, 0.5D);
         break;
      case WEST:
         var10000 = new Vec3(-0.3D, 0.5D, 0.5D);
         break;
      case SOUTH:
         var10000 = new Vec3(0.5D, 0.5D, 1.3D);
         break;
      case NORTH:
         var10000 = new Vec3(0.5D, 0.5D, -0.3D);
         break;
      default:
         throw new IncompatibleClassChangeError();
      }

      return var10000;
   }
}
