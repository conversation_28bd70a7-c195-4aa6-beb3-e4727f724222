package com.github.manasmods.tensura.menu.slot.spatial;

import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import com.github.manasmods.tensura.menu.SpatialStorageMenu;
import com.github.manasmods.tensura.registry.items.TensuraConsumableItems;
import com.github.manasmods.tensura.registry.items.TensuraMaterialItems;
import java.util.Arrays;
import java.util.Optional;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.Container;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.inventory.Slot;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.item.alchemy.PotionUtils;

public class WaterStorageInputSlot extends Slot {
   private final SpatialStorageMenu menu;

   public WaterStorageInputSlot(SpatialStorageMenu menu, Container container, int xPosition, int yPosition) {
      super(container, 0, xPosition, yPosition);
      this.menu = menu;
   }

   public int m_6641_() {
      return 1;
   }

   public boolean m_5857_(ItemStack pStack) {
      if (!PotionUtils.m_43571_(pStack).isEmpty()) {
         return false;
      } else {
         ItemStack output = WaterStorageInputSlot.WaterStorage.getOutputStack(pStack);
         if (output.m_41619_()) {
            return false;
         } else {
            ItemStack currentOutput = this.menu.waterStorageOutput.m_8020_(0);
            if (currentOutput.m_41619_()) {
               return true;
            } else {
               return currentOutput.m_41613_() >= currentOutput.m_41741_() ? false : ItemStack.m_150942_(output, currentOutput);
            }
         }
      }
   }

   public void m_5852_(ItemStack pStack) {
      super.m_5852_(pStack);
      Player player = this.menu.getPlayer();
      WaterStorageInputSlot.WaterStorage[] var3 = WaterStorageInputSlot.WaterStorage.values();
      int var4 = var3.length;

      for(int var5 = 0; var5 < var4; ++var5) {
         WaterStorageInputSlot.WaterStorage water = var3[var5];
         if (water.getInput().equals(pStack.m_41720_())) {
            TensuraSkillCapability.getFrom(player).ifPresent((cap) -> {
               double point = cap.getWaterPoint() + water.getWaterPoint();
               if (!(point < 0.0D) && (!(cap.getWaterPoint() >= 10000.0D) || !(water.getWaterPoint() > 0.0D))) {
                  cap.setWaterPoint(point);
                  TensuraSkillCapability.sync(player);
                  player.m_216990_(this.soundEvent(water.getWaterPoint()));
                  ItemStack currentOutput = this.menu.waterStorageOutput.m_8020_(0);
                  if (currentOutput.m_41619_()) {
                     this.menu.waterStorageOutput.m_6836_(0, water.getOutput().m_7968_());
                  } else {
                     ItemStack newOutput = currentOutput.m_41777_();
                     newOutput.m_41769_(1);
                     this.menu.waterStorageOutput.m_6836_(0, newOutput);
                  }

                  this.menu.waterStorageOutput.m_6596_();
                  this.f_40218_.m_6836_(0, ItemStack.f_41583_);
                  this.f_40218_.m_6596_();
               }
            });
         }
      }

   }

   private SoundEvent soundEvent(double point) {
      return point > 0.0D ? SoundEvents.f_11778_ : SoundEvents.f_11781_;
   }

   public static enum WaterStorage {
      WET_SPONGE(Items.f_41903_, 3.0D, Items.f_41902_),
      HOT_SPRING_WATER((Item)TensuraMaterialItems.HOT_SPRING_WATER_BUCKET.get(), 3.0D, Items.f_42446_),
      WATER_BUCKET(Items.f_42447_, 3.0D, Items.f_42446_),
      MAGIC_WATER((Item)TensuraConsumableItems.WATER_MAGIC_BOTTLE.get(), 1.0D, (Item)TensuraConsumableItems.MAGIC_BOTTLE.get()),
      VACUUMED_MAGIC_WATER((Item)TensuraConsumableItems.VACUUMED_WATER_MAGIC_BOTTLE.get(), 1.0D, (Item)TensuraConsumableItems.MAGIC_BOTTLE.get()),
      WATER_BOTTLE(Items.f_42589_, 1.0D, Items.f_42590_),
      SPLASH_BOTTLE(Items.f_42736_, 1.0D, Items.f_42590_),
      LINGERING_BOTTLE(Items.f_42739_, 1.0D, Items.f_42590_),
      SPONGE(Items.f_41902_, -18.0D, Items.f_41903_),
      BUCKET(Items.f_42446_, -3.0D, Items.f_42447_),
      BOTTLE(Items.f_42590_, -1.0D, Items.f_42589_);

      private final Item input;
      private final double waterPoint;
      private final Item output;

      public static ItemStack getOutputStack(ItemStack input) {
         Optional<Item> output = Arrays.stream(values()).filter((waterStorage) -> {
            return waterStorage.getInput().equals(input.m_41720_());
         }).map(WaterStorageInputSlot.WaterStorage::getOutput).findFirst();
         return (ItemStack)output.map(Item::m_7968_).orElse(ItemStack.f_41583_);
      }

      public Item getInput() {
         return this.input;
      }

      public double getWaterPoint() {
         return this.waterPoint;
      }

      public Item getOutput() {
         return this.output;
      }

      private WaterStorage(Item input, double waterPoint, Item output) {
         this.input = input;
         this.waterPoint = waterPoint;
         this.output = output;
      }

      // $FF: synthetic method
      private static WaterStorageInputSlot.WaterStorage[] $values() {
         return new WaterStorageInputSlot.WaterStorage[]{WET_SPONGE, HOT_SPRING_WATER, WATER_BUCKET, MAGIC_WATER, VACUUMED_MAGIC_WATER, WATER_BOTTLE, SPLASH_BOTTLE, LINGERING_BOTTLE, SPONGE, BUCKET, BOTTLE};
      }
   }
}
