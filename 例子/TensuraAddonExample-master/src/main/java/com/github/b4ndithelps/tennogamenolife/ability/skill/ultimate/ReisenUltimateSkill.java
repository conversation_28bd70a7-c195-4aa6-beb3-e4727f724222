package com.github.b4ndithelps.tennogamenolife.ability.skill.ultimate;

import com.github.b4ndithelps.tennogamenolife.Tenreincarnation;
import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.ability.skill.Skill.SkillType;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import net.minecraft.ChatFormatting;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.EntityHitResult;
import net.minecraft.world.phys.Vec3;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Random;
import java.util.function.Predicate;

/**
 * 典开Rēsen
 */
public class ReisenUltimateSkill extends Skill {
    private final double skillCastCost = 100000.0;     // 施法消耗的魔素值
    private final double learnCost = 4.0;           // 学习难度
    private static final double TARGET_RANGE = 15.0; // 目标检测范围（15格）
    private static final int SKILL_DURATION = 600;    // 技能持续时间（秒）
    private static final int COOLDOWN = 1400;         // 冷却时间（秒）
    private static final Random RANDOM = new Random();
    
    private static final java.util.Map<Player, TensuraSkillInstance> COPIED_SKILLS = new java.util.HashMap<>();

    public ReisenUltimateSkill() {
        super(SkillType.ULTIMATE);
    }

    @Override
    public ResourceLocation getSkillIcon() {
        return new ResourceLocation(Tenreincarnation.MODID, "textures/skill/ultimate/reisen.png");
    }

    @Override
    public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
        return skillCastCost;
    }

    @Override
    public double learningCost() {
        return learnCost;
    }

    /**
     * @param sourceEntity
     * @param maxDistance
     * @return
     */
    private LivingEntity getTargetEntity(LivingEntity sourceEntity, double maxDistance) {
        if (sourceEntity.level.isClientSide) {
            return null;
        }

        Vec3 eyePos = sourceEntity.getEyePosition();
        Vec3 lookVec = sourceEntity.getLookAngle();
        Vec3 targetPos = eyePos.add(lookVec.x * maxDistance, lookVec.y * maxDistance, lookVec.z * maxDistance);
        
        Predicate<Entity> filter = entity -> {
            return entity instanceof LivingEntity && entity != sourceEntity && !entity.isSpectator();
        };
        
        EntityHitResult entityHit = getEntityHitResult(sourceEntity, eyePos, targetPos, 
                new AABB(eyePos.x - maxDistance, eyePos.y - maxDistance, eyePos.z - maxDistance,
                       eyePos.x + maxDistance, eyePos.y + maxDistance, eyePos.z + maxDistance),
                filter, maxDistance * maxDistance);
                
        if (entityHit != null && entityHit.getEntity() instanceof LivingEntity) {
            return (LivingEntity) entityHit.getEntity();
        }
        
        return null;
    }
    
    /**
     * 射线实体检测
     */
    private EntityHitResult getEntityHitResult(Entity source, Vec3 startPos, Vec3 endPos, AABB boundingBox, 
                                             Predicate<Entity> filter, double distanceSq) {
        double closestDistSq = distanceSq;
        Entity closestEntity = null;
        Vec3 closestHitVec = null;
        
        for (Entity entity : source.level.getEntities(source, boundingBox, filter)) {
            AABB entityBox = entity.getBoundingBox().inflate(0.3);
            Optional<Vec3> hitVector = entityBox.clip(startPos, endPos);
            
            if (hitVector.isPresent()) {
                double d = startPos.distanceToSqr(hitVector.get());
                
                if (d < closestDistSq) {
                    closestEntity = entity;
                    closestHitVec = hitVector.get();
                    closestDistSq = d;
                }
            }
        }
        
        return closestEntity != null ? new EntityHitResult(closestEntity, closestHitVec) : null;
    }
    
    /**
     * 清除之前复制的技能
     */
    private void removePreviousCopiedSkill(Player player) {
        TensuraSkillInstance previousSkill = COPIED_SKILLS.get(player);
        if (previousSkill != null) {

            SkillAPI.getSkillsFrom(player).forgetSkill(previousSkill.getSkill());
            COPIED_SKILLS.remove(player);
        }
    }

    @Override
    public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
        if (SkillHelper.outOfMagicule(entity, this.magiculeCost(entity, instance))) {
            return;
        }
        
        if (!(entity instanceof Player)) {
            return;
        }
        
        Player player = (Player) entity;

        LivingEntity target = getTargetEntity(entity, TARGET_RANGE);
        
        if (target != null) {
            removePreviousCopiedSkill(player);
            
            if (target instanceof Player) {
                Player targetPlayer = (Player) target;
                
                List<ManasSkill> ultimateSkills = new ArrayList<>();
                

                SkillAPI.getSkillsFrom(targetPlayer).getLearnedSkills().forEach(skillInstance -> {
                    ManasSkill skill = skillInstance.getSkill();

                    if (skill instanceof Skill && ((Skill) skill).getType() == SkillType.ULTIMATE) {
                        ultimateSkills.add(skill);
                    }
                });
                
                if (!ultimateSkills.isEmpty()) {

                    ManasSkill selectedSkill = ultimateSkills.get(RANDOM.nextInt(ultimateSkills.size()));
                    

                    TensuraSkillInstance tempSkill = new TensuraSkillInstance(selectedSkill);

                    tempSkill.setRemoveTime(SKILL_DURATION);

                    if (SkillUtils.learnSkill(player, tempSkill)) {
                        COPIED_SKILLS.put(player, tempSkill);
                        
                        entity.getLevel().playSound(
                            null,
                            entity.getX(), entity.getY(), entity.getZ(),
                            SoundEvents.EVOKER_CAST_SPELL,
                            SoundSource.PLAYERS,
                            1.0F, 1.0F
                        );

                        instance.setCoolDown(COOLDOWN);

                        player.sendSystemMessage(
                            Component.translatable("tennogamenolife.skill.reisen.success_copy", selectedSkill.getName())
                                .withStyle(Style.EMPTY.withColor(ChatFormatting.GREEN))
                        );

                        player.sendSystemMessage(
                            Component.translatable("tennogamenolife.skill.reisen.success_copy_duration", 
                                target.getName().getString(), SKILL_DURATION)
                                .withStyle(Style.EMPTY.withColor(ChatFormatting.GOLD))
                        );
                    } else {
                        player.sendSystemMessage(
                            Component.translatable("tennogamenolife.skill.reisen.cannot_copy")
                                .withStyle(Style.EMPTY.withColor(ChatFormatting.RED))
                        );
                    }
                } else {
                    player.sendSystemMessage(
                        Component.translatable("tennogamenolife.skill.reisen.no_ultimate_skill")
                            .withStyle(Style.EMPTY.withColor(ChatFormatting.RED))
                    );
                }
            } else {
                player.sendSystemMessage(
                    Component.translatable("tennogamenolife.skill.reisen.only_player_skill")
                        .withStyle(Style.EMPTY.withColor(ChatFormatting.RED))
                );
            }
        } else {
            player.sendSystemMessage(
                Component.translatable("tennogamenolife.skill.reisen.no_target")
                    .withStyle(Style.EMPTY.withColor(ChatFormatting.RED))
            );
        }
    }
} 