package com.github.manasmods.tensura.entity.human;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.api.entity.ai.CrossbowAttackGoal;
import com.github.manasmods.tensura.api.entity.ai.WanderingFollowOwnerGoal;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.config.SpawnRateConfig;
import com.github.manasmods.tensura.entity.AkashEntity;
import com.github.manasmods.tensura.entity.IfritEntity;
import com.github.manasmods.tensura.entity.SylphideEntity;
import com.github.manasmods.tensura.entity.UndineEntity;
import com.github.manasmods.tensura.entity.WarGnomeEntity;
import com.github.manasmods.tensura.entity.magic.TensuraProjectile;
import com.github.manasmods.tensura.entity.magic.field.Hellfire;
import com.github.manasmods.tensura.entity.magic.projectile.FireBallProjectile;
import com.github.manasmods.tensura.entity.magic.skill.HeatSphereProjectile;
import com.github.manasmods.tensura.entity.template.HumanoidNPCEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.RequestFxSpawningPacket;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.items.TensuraArmorItems;
import com.github.manasmods.tensura.registry.items.TensuraToolItems;
import com.github.manasmods.tensura.registry.magic.SpiritualMagics;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.Iterator;
import java.util.List;
import javax.annotation.Nullable;
import net.minecraft.commands.arguments.EntityAnchorArgument.Anchor;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.core.particles.SimpleParticleType;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.tags.BiomeTags;
import net.minecraft.util.RandomSource;
import net.minecraft.world.DifficultyInstance;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.Pose;
import net.minecraft.world.entity.SpawnGroupData;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.FloatGoal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.RangedBowAttackGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.WaterAvoidingRandomStrollGoal;
import net.minecraft.world.entity.ai.goal.target.NonTameRandomTargetGoal;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.ItemLike;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ServerLevelAccessor;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.ForgeMod;
import net.minecraftforge.common.Tags.Biomes;
import net.minecraftforge.network.PacketDistributor;

public class ShizuEntity extends OtherworlderEntity {
   private static final EntityDataAccessor<Integer> TRANSFORM_TICK;
   private static final EntityDataAccessor<Boolean> DYING;

   public ShizuEntity(EntityType<? extends ShizuEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.f_19793_ = 2.0F;
      this.f_21365_ = new ShizuEntity.TransformLookControl();
      this.f_21342_ = new ShizuEntity.TransformMoveControl();
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22284_, 5.0D).m_22268_(Attributes.f_22281_, 10.0D).m_22268_(Attributes.f_22276_, 60.0D).m_22268_(Attributes.f_22279_, 0.25D).m_22268_(Attributes.f_22277_, 32.0D).m_22268_(Attributes.f_22278_, 0.20000000298023224D).m_22268_((Attribute)ForgeMod.SWIM_SPEED.get(), 1.0D).m_22268_((Attribute)ForgeMod.ATTACK_RANGE.get(), 2.0D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(0, new FloatGoal(this));
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(2, new HumanoidNPCEntity.EatingItemGoal(this, (entity) -> {
         return this.shouldHeal();
      }, 3.0F));
      this.f_21345_.m_25352_(3, new CrossbowAttackGoal(this, 1.2D, 20.0F));
      this.f_21345_.m_25352_(3, new RangedBowAttackGoal(this, 1.0D, 20, 20.0F));
      this.f_21345_.m_25352_(3, new HumanoidNPCEntity.SpearTypeAttackGoal(this, 1.0D, 20, 20.0F));
      this.f_21345_.m_25352_(3, new ShizuEntity.ShizuAttackGoal(this));
      this.f_21345_.m_25352_(4, new WanderingFollowOwnerGoal(this, 1.5D, 10.0F, 5.0F, false));
      this.f_21345_.m_25352_(7, new WaterAvoidingRandomStrollGoal(this, 1.2D));
      this.f_21345_.m_25352_(8, new RandomLookAroundGoal(this));
      this.f_21345_.m_25352_(9, new LookAtPlayerGoal(this, Player.class, 6.0F));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(3, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this, new Class[]{ShizuEntity.class})).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(6, new NonTameRandomTargetGoal(this, Player.class, false, this::shouldAttackPlayer));
      this.f_21346_.m_25352_(8, new ResetUniversalAngerTargetGoal(this, true));
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(TRANSFORM_TICK, 0);
      this.f_19804_.m_135372_(DYING, false);
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128405_("TransformTick", this.getTransformTick());
      compound.m_128379_("Dying", this.isDying());
   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      this.setTransformTick(compound.m_128451_("TransformTick"));
      this.setDying(compound.m_128471_("Dying"));
   }

   public int getTransformTick() {
      return (Integer)this.f_19804_.m_135370_(TRANSFORM_TICK);
   }

   public void setTransformTick(int tick) {
      this.f_19804_.m_135381_(TRANSFORM_TICK, tick);
   }

   public boolean isDying() {
      return (Boolean)this.f_19804_.m_135370_(DYING);
   }

   public void setDying(boolean dying) {
      this.f_19804_.m_135381_(DYING, dying);
   }

   public ResourceLocation getTextureLocation() {
      return new ResourceLocation("tensura", "textures/entity/otherworlder/shizu.png");
   }

   public List<ManasSkill> getUniqueSkills() {
      return List.of((ManasSkill)UniqueSkills.DEGENERATE.get());
   }

   public boolean m_6060_() {
      return TensuraEffectsCapability.hasSyncedEffect(this, (MobEffect)TensuraMobEffects.BLACK_BURN.get());
   }

   public boolean m_142535_(float pFallDistance, float pMultiplier, DamageSource pSource) {
      return false;
   }

   public boolean m_6785_(double pDistanceToClosestPlayer) {
      return false;
   }

   public void m_8119_() {
      super.m_8119_();
      if (!this.m_9236_().m_5776_()) {
         if (this.isDying()) {
            if (this.f_19797_ % 100 == 0) {
               this.m_6469_(TensuraDamageSources.OUT_OF_ENERGY, 5.0F);
               TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123796_);
               TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123798_);
            }

         } else if (this.getTransformTick() < 100) {
            if (!(this.m_21223_() > this.m_21233_() / 3.0F)) {
               this.setSleeping(false);
               this.setTransformTick(this.getTransformTick() + 1);
               this.m_21573_().m_26573_();
               if (this.f_19797_ % 5 == 0) {
                  this.combust(false);
               }

               this.f_19853_.m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_11862_, SoundSource.NEUTRAL, 0.5F, 0.4F + this.f_19796_.m_188501_() * 0.4F + 0.8F);
               if (this.getTransformTick() >= 100) {
                  this.summonIfrit(this.m_5448_());
               }

            }
         }
      }
   }

   protected void updatePoses() {
      if (this.isDying()) {
         this.m_20124_(Pose.SLEEPING);
      } else if (this.getTransformTick() > 1) {
         this.m_20124_(Pose.STANDING);
      } else {
         super.updatePoses();
      }

   }

   protected void sleepHandler() {
      if (!this.isDying()) {
         if (this.getTransformTick() <= 1) {
            super.sleepHandler();
         }
      }
   }

   public boolean m_6469_(DamageSource pSource, float pAmount) {
      boolean hurt = super.m_6469_(pSource, pAmount);
      if (hurt) {
         Entity var5 = pSource.m_7639_();
         if (var5 instanceof LivingEntity) {
            LivingEntity source = (LivingEntity)var5;
            if (!source.m_6084_()) {
               return true;
            }

            if (source instanceof Player) {
               Player player = (Player)source;
               if (player.m_7500_() || player.m_5833_()) {
                  return true;
               }
            }

            source.m_7311_(Math.max(200, source.m_20094_()));
         }
      }

      return hurt;
   }

   public boolean m_7327_(Entity pEntity) {
      if (super.m_7327_(pEntity) && pEntity instanceof LivingEntity) {
         LivingEntity target = (LivingEntity)pEntity;
         target.m_7311_(Math.max(200, target.m_20094_()));
         return true;
      } else {
         return false;
      }
   }

   private void fireBallShoot(boolean heatWave) {
      if (this.m_5448_() != null) {
         this.m_7618_(Anchor.EYES, this.m_5448_().m_146892_());
      }

      this.m_21011_(InteractionHand.OFF_HAND, true);
      Object fireBall;
      if (heatWave) {
         fireBall = new HeatSphereProjectile(this.f_19853_, this);
         ((TensuraProjectile)fireBall).setEffectRange(2.5F);
         ((TensuraProjectile)fireBall).setSkill((ManasSkillInstance)SkillAPI.getSkillsFrom(this).getSkill((ManasSkill)ExtraSkills.HEAT_WAVE.get()).orElse((Object)null));
      } else {
         fireBall = new FireBallProjectile(this.f_19853_, this);
         ((TensuraProjectile)fireBall).setSpiritAttack(true);
         ((TensuraProjectile)fireBall).setSkill((ManasSkillInstance)SkillAPI.getSkillsFrom(this).getSkill((ManasSkill)SpiritualMagics.FIRE_BOLT.get()).orElse((Object)null));
      }

      ((TensuraProjectile)fireBall).setMpCost(100.0D);
      ((TensuraProjectile)fireBall).setSpeed(1.2F);
      ((TensuraProjectile)fireBall).setDamage((float)(this.m_21133_(Attributes.f_22281_) * 2.0D));
      ((TensuraProjectile)fireBall).setBurnTicks(20);
      ((TensuraProjectile)fireBall).m_20242_(true);
      ((TensuraProjectile)fireBall).setPosAndShoot(this);
      this.f_19853_.m_7967_((Entity)fireBall);
      this.f_19853_.m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_11705_, SoundSource.NEUTRAL, 0.5F, 0.4F + this.f_19796_.m_188501_() * 0.4F + 0.8F);
   }

   private void hellFire() {
      Entity target = this.m_5448_();
      Vec3 pos;
      if (target != null) {
         pos = target.m_20182_().m_82520_(0.0D, (double)(target.m_20206_() / 2.0F), 0.0D);
      } else {
         BlockHitResult result = SkillHelper.getPlayerPOVHitResult(this.f_19853_, this, Fluid.NONE, 15.0D);
         pos = result.m_82450_().m_82520_(0.0D, 0.5D, 0.0D);
      }

      Hellfire sphere = new Hellfire(this.m_9236_(), this);
      sphere.setDamage(250.0F);
      sphere.setMpCost(10000.0D);
      sphere.setSkill((ManasSkillInstance)SkillAPI.getSkillsFrom(this).getSkill((ManasSkill)SpiritualMagics.HELLFIRE.get()).orElse((Object)null));
      sphere.setLife(60);
      sphere.setRadius(2.5F);
      sphere.m_6034_(pos.f_82479_, pos.f_82480_ - (double)sphere.getRadius(), pos.f_82481_);
      this.m_9236_().m_7967_(sphere);
      this.m_21011_(InteractionHand.OFF_HAND, true);
      this.m_9236_().m_6263_((Player)null, sphere.m_20185_(), sphere.m_20186_(), sphere.m_20189_(), SoundEvents.f_11913_, SoundSource.PLAYERS, 1.0F, 1.0F);
      this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_11862_, SoundSource.PLAYERS, 1.0F, 1.0F);
      ((ServerLevel)this.m_9236_()).m_8767_((SimpleParticleType)TensuraParticles.RED_FIRE.get(), this.m_20185_(), this.m_20186_() + (double)this.m_20206_() / 2.0D, this.m_20189_(), 10, 0.08D, 0.08D, 0.08D, 0.15D);
      TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
         return sphere;
      }), new RequestFxSpawningPacket(new ResourceLocation("tensura:fire_sphere_5x5"), sphere.m_19879_(), 0.0D, (double)sphere.getRadius(), 0.0D, false));
   }

   private void combust(boolean constant) {
      if (constant) {
         this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_11913_, SoundSource.PLAYERS, 1.0F, 1.0F);
      }

      TensuraParticleHelper.spawnServerParticles(this.f_19853_, (ParticleOptions)TensuraParticles.RED_FIRE.get(), this.m_20185_(), this.m_20188_(), this.m_20189_(), 55, 0.08D, 0.08D, 0.08D, 0.2D, true);
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, (ParticleOptions)TensuraParticles.HEAT_EFFECT.get(), this.m_20185_(), this.m_20188_(), this.m_20189_(), 55, 0.08D, 0.08D, 0.08D, 0.2D, true);
      AABB aabb = this.m_20191_().m_82400_(this.m_21133_((Attribute)ForgeMod.ATTACK_RANGE.get()) + 5.0D);
      List<LivingEntity> list = this.f_19853_.m_6443_(LivingEntity.class, aabb, (entity) -> {
         return !entity.m_7307_(this) && entity != this.m_21826_() && entity != this;
      });
      if (!list.isEmpty()) {
         Iterator var4 = list.iterator();

         while(var4.hasNext()) {
            LivingEntity target = (LivingEntity)var4.next();
            target.m_6469_(TensuraDamageSources.heatWave(this), (float)this.m_21133_(Attributes.f_22281_));
            target.m_20334_(0.0D, 0.1D, 0.0D);
            SkillHelper.knockBack(this, target, constant ? 1.0F : 2.0F);
         }

      }
   }

   private void summonIfrit(@Nullable LivingEntity target) {
      TensuraEPCapability.setSkippingEPDrop(this, true);
      Mob entity = this.getSpiritSummoning();
      entity.m_146884_(this.m_20182_());
      entity.m_6710_(target);
      this.m_9236_().m_7967_(entity);
      this.m_146870_();
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, ParticleTypes.f_123812_, this.m_20185_(), this.m_20186_() + (double)(this.m_20206_() / 2.0F), this.m_20189_(), 55, 0.08D, 0.08D, 0.08D, 0.5D, true);
      this.f_19853_.m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_11913_, SoundSource.NEUTRAL, 0.5F, 0.4F + this.f_19796_.m_188501_() * 0.4F + 0.8F);
   }

   private Mob getSpiritSummoning() {
      if (SpawnRateConfig.rollChance((Integer)SpawnRateConfig.INSTANCE.shizuChance.get(), this.f_19796_)) {
         if (this.m_9236_().m_204166_(this.m_20183_()).m_203656_(BiomeTags.f_207603_)) {
            UndineEntity entity = new UndineEntity((EntityType)TensuraEntityTypes.UNDINE.get(), this.f_19853_);
            entity.setShizuNBT(this.serializeNBT());
            entity.setMiscAnimation(7);
            return entity;
         }

         if (this.m_9236_().m_204166_(this.m_20183_()).m_203656_(Biomes.IS_PEAK)) {
            SylphideEntity entity = new SylphideEntity((EntityType)TensuraEntityTypes.SYLPHIDE.get(), this.f_19853_);
            entity.setShizuNBT(this.serializeNBT());
            entity.setMiscAnimation(7);
            return entity;
         }

         if (this.m_9236_().m_204166_(this.m_20183_()).m_203656_(BiomeTags.f_215818_)) {
            AkashEntity entity = new AkashEntity((EntityType)TensuraEntityTypes.AKASH.get(), this.f_19853_);
            entity.setShizuNBT(this.serializeNBT());
            entity.setMiscAnimation(4);
            return entity;
         }

         if (this.m_9236_().m_204166_(this.m_20183_()).m_203656_(Biomes.IS_CAVE)) {
            WarGnomeEntity entity = new WarGnomeEntity((EntityType)TensuraEntityTypes.WAR_GNOME.get(), this.f_19853_);
            entity.setShizuNBT(this.serializeNBT());
            entity.setMiscAnimation(7);
            return entity;
         }
      }

      IfritEntity entity = new IfritEntity((EntityType)TensuraEntityTypes.IFRIT.get(), this.f_19853_);
      entity.setShizuNBT(this.serializeNBT());
      entity.setMiscAnimation(7);
      return entity;
   }

   @Nullable
   public SpawnGroupData m_6518_(ServerLevelAccessor pLevel, DifficultyInstance pDifficulty, MobSpawnType pReason, @Nullable SpawnGroupData pSpawnData, @Nullable CompoundTag pDataTag) {
      if (!pReason.equals(MobSpawnType.BUCKET)) {
         this.m_213945_(this.f_19796_, pDifficulty);
      }

      return super.m_6518_(pLevel, pDifficulty, pReason, pSpawnData, pDataTag);
   }

   protected void m_213945_(RandomSource pRandom, DifficultyInstance pDifficulty) {
      ItemStack mask = new ItemStack((ItemLike)TensuraArmorItems.ANTI_MAGIC_MASK.get());
      this.m_8061_(EquipmentSlot.HEAD, mask);
      this.inventory.m_6836_(0, mask);
      this.inventory.m_6596_();
      ItemStack stack = new ItemStack((ItemLike)TensuraToolItems.PURE_MAGISTEEL_LONG_SWORD.get());
      this.m_8061_(EquipmentSlot.MAINHAND, stack);
      this.inventory.m_6836_(4, stack);
      this.inventory.m_6596_();
   }

   public void m_6667_(DamageSource source) {
      if (this.getTransformTick() < 100) {
         LivingEntity target = this.m_5448_();
         if (target == null) {
            Entity var4 = source.m_7639_();
            if (var4 instanceof LivingEntity) {
               LivingEntity living = (LivingEntity)var4;
               target = living;
            }
         }

         this.summonIfrit(target);
      } else {
         super.m_6667_(source);
      }
   }

   static {
      TRANSFORM_TICK = SynchedEntityData.m_135353_(ShizuEntity.class, EntityDataSerializers.f_135028_);
      DYING = SynchedEntityData.m_135353_(ShizuEntity.class, EntityDataSerializers.f_135035_);
   }

   public class TransformLookControl extends TensuraTamableEntity.SleepLookControl {
      public TransformLookControl() {
         super();
      }

      public void m_8128_() {
         if (!ShizuEntity.this.isDying()) {
            if (ShizuEntity.this.getTransformTick() < 1) {
               super.m_8128_();
            }

         }
      }
   }

   public class TransformMoveControl extends TensuraTamableEntity.SleepMoveControl {
      public TransformMoveControl() {
         super();
      }

      public void m_8126_() {
         if (!ShizuEntity.this.isDying()) {
            if (ShizuEntity.this.getTransformTick() < 1) {
               super.m_8126_();
            }

         }
      }
   }

   public class ShizuAttackGoal extends HumanoidNPCEntity.NPCMeleeAttackGoal {
      public final ShizuEntity entity;

      public ShizuAttackGoal(ShizuEntity entity) {
         super(entity, 2.0D, true);
         this.entity = entity;
      }

      public boolean m_8036_() {
         if (this.entity.isDying()) {
            return false;
         } else {
            return this.entity.getTransformTick() >= 1 ? false : super.m_8036_();
         }
      }

      public boolean m_8045_() {
         if (this.entity.isDying()) {
            return false;
         } else {
            return this.entity.getTransformTick() >= 1 ? false : super.m_8045_();
         }
      }

      protected void m_6739_(LivingEntity pEnemy, double pDistToEnemySqr) {
         double d0 = this.m_6639_(pEnemy);
         int randomAttack = this.randomAttack(pEnemy, pDistToEnemySqr);
         if (randomAttack != 0) {
            double var10000;
            switch(randomAttack) {
            case 2:
               var10000 = d0 + 900.0D;
               break;
            case 3:
               var10000 = d0 + 200.0D;
               break;
            case 4:
               var10000 = d0 + 25.0D;
               break;
            default:
               var10000 = d0;
            }

            double attackRange = var10000;
            if (pDistToEnemySqr <= attackRange && this.m_25564_()) {
               this.m_25563_();
               switch(randomAttack) {
               case 2:
                  this.entity.fireBallShoot(this.entity.m_217043_().m_188503_(5) == 1);
                  break;
               case 3:
                  this.entity.m_21573_().m_26573_();
                  this.entity.hellFire();
                  break;
               case 4:
                  this.entity.m_21573_().m_26573_();
                  this.entity.combust(true);
                  break;
               default:
                  this.entity.m_7327_(pEnemy);
               }

               this.entity.m_21011_(InteractionHand.MAIN_HAND, true);
            }

         }
      }

      protected int randomAttack(LivingEntity target, double distSqr) {
         if ((this.entity.f_19796_.m_188503_(5) != 1 || !(distSqr > this.m_6639_(target))) && this.entity.f_19796_.m_188503_(10) != 1) {
            return (double)this.entity.f_19796_.m_188501_() <= 0.1D ? 4 : 1;
         } else {
            return (double)this.entity.f_19796_.m_188501_() <= 0.05D ? 3 : 2;
         }
      }
   }
}
