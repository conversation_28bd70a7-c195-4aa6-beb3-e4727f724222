package com.github.b4ndithelps.tennogamenolife.ability.skill.ultimate;

import com.github.b4ndithelps.tennogamenolife.Tenreincarnation;
import com.github.b4ndithelps.tennogamenolife.client.render.ChargeBarRenderer;
import com.github.b4ndithelps.tennogamenolife.effect.WeakenEffect;
import com.github.b4ndithelps.tennogamenolife.registry.ModEffects;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import com.github.manasmods.tensura.util.damage.TensuraDamageSource;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.ClipContext;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraft.world.level.Explosion;
import net.minecraft.network.chat.Component;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.material.Material;
import net.minecraft.world.effect.MobEffectInstance;
import java.util.List;

public class TengekiUltimateSkill extends Skill {
    private final double skillCastCost = 100000.0;     // 施法消耗的魔素值
    private final double learnCost = 5.0;           // 学习难度
    private static final double MAX_RADIUS = 200.0;  // 最大半径
    private static final double MIN_RADIUS = 5.0;   // 最小半径
    private static final double TARGET_RANGE = 100.0; // 目标检测范围（100格）
    public static final long MIN_CHARGE_TIME = 5000;  // 最小蓄力时间（5秒）
    public static final long MAX_CHARGE_TIME = 60000; // 最大蓄力时间（60秒）
    private boolean isCharging = false; // 是否正在蓄力
    private long chargeStartTime = 0; // 开始蓄力的时间
    private long lastConsumeTime = 0; // 上次消耗魔素的时间
    private Vec3 targetPos; // 目标位置
    private Vec3 startPos; // 开始蓄力时的位置
    private static final ResourceLocation FORBIDDEN_DIMENSION = new ResourceLocation("tensura", "labyrinth");

    public TengekiUltimateSkill() {
        super(SkillType.ULTIMATE);
    }

    @Override
    public ResourceLocation getSkillIcon() {
        return new ResourceLocation(Tenreincarnation.MODID, "textures/skill/ultimate/tengeki.png");
    }

    @Override
    public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
        return skillCastCost;
    }

    @Override
    public double learningCost() {
        return learnCost;
    }

    @Override
    public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
        // 检查是否在禁止使用的维度
        if (isInForbiddenDimension(entity)) {
            if (entity instanceof Player player) {
                player.displayClientMessage(Component.translatable("skill.tengeki.forbidden_dimension"), true);
            }
            return;
        }
        
        if (SkillHelper.outOfMagicule(entity, this.magiculeCost(entity, instance))) {
            return;
        }
        
        isCharging = true;
        chargeStartTime = System.currentTimeMillis();
        lastConsumeTime = chargeStartTime; 
        targetPos = findTargetPosition(entity);
        ChargeBarRenderer.startCharging();
    }

    @Override
    public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
        // 如果在蓄力过程中进入禁止维度，停止蓄力
        if (isCharging && isInForbiddenDimension(entity)) {
            isCharging = false;
            ChargeBarRenderer.stopCharging();
            if (entity instanceof Player player) {
                player.displayClientMessage(Component.translatable("skill.tengeki.forbidden_dimension"), true);
            }
            return false;
        }
        return isCharging; 
    }
    
    /**
     * 检查实体是否在禁止使用的维度
     */
    private boolean isInForbiddenDimension(LivingEntity entity) {
        ResourceLocation dimensionLocation = entity.level.dimension().location();
        return FORBIDDEN_DIMENSION.equals(dimensionLocation);
    }

    @Override
    public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
        if (isCharging) {
            long chargeTime = System.currentTimeMillis() - chargeStartTime;
            if (chargeTime >= MIN_CHARGE_TIME) {
                double chargeProgress = Math.min(1.0, (double)chargeTime / MAX_CHARGE_TIME);
                double currentRadius = MIN_RADIUS + (MAX_RADIUS - MIN_RADIUS) * chargeProgress;
                
                this.triggerExplosion(instance, entity, currentRadius);
                instance.setCoolDown(3600);
            }
            isCharging = false;
            ChargeBarRenderer.stopCharging();
        }
    }

    @Override
    public void onTick(ManasSkillInstance instance, LivingEntity entity) {
        if (isCharging) {
            long currentTime = System.currentTimeMillis();
            long chargeTime = currentTime - chargeStartTime;
            // 如果蓄力时间超过10秒，开始每秒消耗1000魔素
            if (chargeTime >= 10000) {
                if (currentTime - lastConsumeTime >= 1000) { 
                    if (entity instanceof Player player) {
                        double remainingCost = SkillHelper.outOfMagiculeStillConsume(player, 15000.0);
                        if (remainingCost > 0) {
                            player.die(new DamageSource("magicule_explosion") {
                                @Override
                                public Component getLocalizedDeathMessage(LivingEntity entity) {
                                    return Component.translatable("death.attack.magicule_explosion", entity.getName());
                                }
                            });
                            isCharging = false;
                            ChargeBarRenderer.stopCharging();
                            return;
                        }
                        lastConsumeTime = currentTime; 
                    }
                }
            }
        }
    }

    private Vec3 findTargetPosition(LivingEntity entity) {
        if (!(entity instanceof Player player)) return null;
        
        Level level = player.level;
        
        Vec3 startPos = player.getEyePosition(1.0F);
        
        Vec3 lookVec = player.getViewVector(1.0F);
        
        Vec3 endPos = startPos.add(lookVec.scale(TARGET_RANGE));
        
        BlockHitResult hitResult = level.clip(
            new ClipContext(startPos, endPos, ClipContext.Block.COLLIDER, ClipContext.Fluid.NONE, player)
        );
        
        if (hitResult.getType() != BlockHitResult.Type.MISS) {
            return hitResult.getLocation();
        }
        
        return endPos;
    }

    private void triggerExplosion(ManasSkillInstance instance, LivingEntity entity, double radius) {
        if (targetPos == null) return;

        Level level = entity.getLevel();
        BlockPos centerPos = new BlockPos((int)targetPos.x, (int)targetPos.y, (int)targetPos.z);

        long chargeTime = System.currentTimeMillis() - chargeStartTime;
        float explosionPower = (float)radius;
        
        if (chargeTime >= 5000) {
            explosionPower *= 1.5f;
        }
        if (chargeTime >= 20000) {
            explosionPower *= 1.5f;
        }
        if (chargeTime >= 40000) {
            explosionPower *= 1.5f;
        }
        if (chargeTime >= 59999) {
            explosionPower *= 2.0f;
        }

        if (chargeTime >= 20000 && entity instanceof Player player) {
            player.addEffect(new MobEffectInstance(ModEffects.WEAKEN.get(), 20 * 60 * 10, 0));
            player.addEffect(new MobEffectInstance(net.minecraft.world.effect.MobEffects.WEAKNESS, 20 * 60 * 10, 1));
        }

        level.playSound(
            null, targetPos.x, targetPos.y, targetPos.z,
            SoundEvents.GENERIC_EXPLODE, SoundSource.PLAYERS,
            1.0F, 1.0F
        );

        DamageSource magicDamage;
        if (chargeTime >= 35000) {
            TensuraDamageSource tensuraDamageSource = (TensuraDamageSource) TensuraDamageSources.elementalAttack("tensura.magic_attack", entity, this.magiculeCost(entity, instance), instance, true);
            tensuraDamageSource.setIgnoreResistance(2.0f);
            magicDamage = tensuraDamageSource;
        } else {
            magicDamage = TensuraDamageSources.elementalAttack("tensura.magic_attack", entity, this.magiculeCost(entity, instance), instance, true);
        }
        
        List<LivingEntity> entities = level.getEntitiesOfClass(LivingEntity.class, 
            new AABB(centerPos).inflate(radius),
            e -> !e.isAlliedTo(entity) && e.isAlive()
        );
        
        for (LivingEntity target : entities) {
            DamageSourceHelper.dealSplitElementalDamage(target, magicDamage, 0.9f, explosionPower * 2.0f);
        }

        level.explode(entity, targetPos.x, targetPos.y, targetPos.z, 
            explosionPower, Explosion.BlockInteraction.DESTROY);

        createCrater(level, centerPos, (int)radius);

        for (int i = 0; i < 50; i++) {
            double angle = level.random.nextDouble() * Math.PI * 2;
            double distance = level.random.nextDouble() * radius;
            double x = targetPos.x + Math.cos(angle) * distance;
            double y = targetPos.y + level.random.nextDouble() * radius;
            double z = targetPos.z + Math.sin(angle) * distance;
            level.addParticle(ParticleTypes.EXPLOSION_EMITTER, x, y, z, 0, 0, 0);
        }
    }

    private void createCrater(Level level, BlockPos center, int radius) {
        for (int x = -radius; x <= radius; x++) {
            for (int z = -radius; z <= radius; z++) {

                double distance = Math.sqrt(x * x + z * z);
                if (distance <= radius) {

                    int height = (int)(Math.sqrt(radius * radius - distance * distance) * 0.5);
                    

                    for (int y = center.getY() + height; y >= center.getY() - height; y--) {
                        BlockPos pos = center.offset(x, y - center.getY(), z);
                        BlockState state = level.getBlockState(pos);
                        

                        if (state.getMaterial().isSolid()) {
                            level.setBlock(pos, Blocks.AIR.defaultBlockState(), 3);
                        }
                    }
                }
            }
        }


        if (radius > 50) {

            for (int x = -radius; x <= radius; x++) {
                for (int z = -radius; z <= radius; z++) {
                    double distance = Math.sqrt(x * x + z * z);
                    

                    if (distance >= radius * 0.8 && distance <= radius) {
                        if (level.random.nextFloat() < 0.2) { 
                            BlockPos pos = center.offset(x, center.getY(), z);
                            if (level.getBlockState(pos).isAir()) {
                                if (level.random.nextFloat() < 0.8) { 
                                    level.setBlock(pos, Blocks.OBSIDIAN.defaultBlockState(), 3);
                                } else { 
                                    level.setBlock(pos, Blocks.GLASS.defaultBlockState(), 3);
                                }
                            }
                        }
                    }
                    

                    if (distance <= radius * 0.3) {
                        if (level.random.nextFloat() < 0.05) { 
                            BlockPos pos = center.offset(x, center.getY(), z);
                            if (level.getBlockState(pos).isAir()) {
                                if (level.random.nextFloat() < 0.7) { 
                                    level.setBlock(pos, Blocks.OBSIDIAN.defaultBlockState(), 3);
                                } else { 
                                    level.setBlock(pos, Blocks.GLASS.defaultBlockState(), 3);
                                }
                            }
                        }
                    }
                }
            }
        }
    }
} 