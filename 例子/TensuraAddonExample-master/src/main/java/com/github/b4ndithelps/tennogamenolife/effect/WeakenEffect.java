package com.github.b4ndithelps.tennogamenolife.effect;

import net.minecraft.world.effect.MobEffectCategory;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.ai.attributes.AttributeMap;
import com.github.manasmods.tensura.effect.template.TensuraMobEffect;
import net.minecraft.resources.ResourceLocation;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;
import net.minecraftforge.client.extensions.common.IClientMobEffectExtensions;
import java.util.UUID;
import java.util.function.Consumer;
import net.minecraft.world.effect.MobEffectInstance;

public class WeakenEffect extends TensuraMobEffect {
    private static final UUID WEAKEN_SIZE_UUID = UUID.fromString("12345678-1234-1234-1234-123456789012");

    public WeakenEffect() {
        super(MobEffectCategory.NEUTRAL, 0x808080); // 灰色
    }

    @Override
    public void applyEffectTick(LivingEntity entity, int amplifier) {
        if (entity instanceof Player player) {
            AttributeInstance sizeInstance = entity.getAttribute(TensuraAttributeRegistry.SIZE.get());
            if (sizeInstance != null) {
                AttributeModifier sizeModifier = new AttributeModifier(WEAKEN_SIZE_UUID, "Weaken Size", 0.6f - 1.0f, Operation.MULTIPLY_TOTAL);
                if (!sizeInstance.hasModifier(sizeModifier)) {
                    sizeInstance.addTransientModifier(sizeModifier);
                }
            }
        }
    }

    @Override
    public void removeAttributeModifiers(LivingEntity entity, AttributeMap attributeMap, int amplifier) {
        super.removeAttributeModifiers(entity, attributeMap, amplifier);
        if (entity instanceof Player player) {
            AttributeInstance sizeInstance = entity.getAttribute(TensuraAttributeRegistry.SIZE.get());
            if (sizeInstance != null) {
                sizeInstance.removeModifier(WEAKEN_SIZE_UUID);
            }
        }
    }

    @Override
    public boolean isDurationEffectTick(int duration, int amplifier) {
        return true; // 每tick都应用效果
    }

    @Override
    @OnlyIn(Dist.CLIENT)
    public void initializeClient(Consumer<IClientMobEffectExtensions> consumer) {
        consumer.accept(new IClientMobEffectExtensions() {
            @Override
            public boolean isVisibleInInventory(MobEffectInstance instance) {
                return true;
            }

            @Override
            public boolean isVisibleInGui(MobEffectInstance instance) {
                return true;
            }
        });
    }
} 