package com.github.manasmods.tensura.effect.skill;

import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.effect.template.MobEffectHelper;
import com.github.manasmods.tensura.effect.template.SkillMobEffect;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import net.minecraft.world.effect.MobEffectCategory;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attribute;

public class SelfRegenerationEffect extends SkillMobEffect {
   public SelfRegenerationEffect(MobEffectCategory pCategory, int pColor) {
      super(pCategory, pColor);
   }

   public void m_6742_(LivingEntity pLivingEntity, int pAmplifier) {
      if (!(pLivingEntity.m_21223_() >= pLivingEntity.m_21233_()) && pLivingEntity.m_6084_()) {
         if (!MobEffectHelper.shouldCancelHeal(pLivingEntity)) {
            pLivingEntity.m_5634_((float)(2 * (pAmplifier + 1)));
            if (pAmplifier > 0) {
               TensuraEPCapability.setSpiritualHealth(pLivingEntity, Math.min(TensuraEPCapability.getSpiritualHealth(pLivingEntity) + (double)(4 * (pAmplifier + 1)), pLivingEntity.m_21133_((Attribute)TensuraAttributeRegistry.MAX_SPIRITUAL_HEALTH.get())));
            }

         }
      }
   }

   public boolean m_6584_(int pDuration, int pAmplifier) {
      return pDuration % 20 == 0;
   }
}
