{"tennogamenolife.skill.magic_beads": "魔素提取", "tennogamenolife.skill.magic_beads.description": "将自身魔素提取压缩成一颗", "item.tennogamenolife.mag": "混沌魔珠", "item.tennogamenolife.mag.desc": "对着生物右键能够增加生物1万存在值，那也是你自身的魔素炼制而成！", "tennogamenolife.race.machine": "机凯种", "tennogamenolife.race.machine.notes": "由智慧之神创造的种族，拥有强大的分析和战斗能力。", "tennogamenolife.race.befehler": "指挥体", "tennogamenolife.race.befehler.description": "指挥体是机凯种的进化形态，具有更高的防御力和魔素值。", "tennogamenolife.race.kampf": "战斗体", "tennogamenolife.race.kampf.description": "战斗体是机凯种的进化形态，具有更高的攻击力和魔素值。", "tennogamenolife.race.zeichen": "设计体", "tennogamenolife.race.zeichen.description": "设计体是机凯种的进化形态，具有更高的创造力和魔素值。", "tennogamenolife.race.pruefer": "解析体", "tennogamenolife.race.pruefer.description": "解析体是机凯种的进化形态，具有更高的分析力和魔素值。", "tennogamenolife.race.seher": "观测体", "tennogamenolife.race.seher.description": "观测体是机凯种的进化形态，具有更高的观察力和魔素值。", "tennogamenolife.race.hubie_dora": "休比·多拉", "tennogamenolife.race.hubie_dora.description": "休比·多拉是机凯种的进化形态，具有更高的综合能力和魔素值。", "tennogamenolife.race.einzeig": "爱因兹希", "tennogamenolife.race.einzeig.description": "爱因兹希是指挥体的进化形态，具有更高的防御力和魔素值，以及更强的空间操控能力。", "tennogamenolife.race.emir_eins": "依蜜尔爱因", "tennogamenolife.race.emir_eins.description": "依蜜尔爱因是指挥体的进化形态，具有更高的防御力和魔素值，以及更强的空间操控能力。", "tennogamenolife.race.horou": "§6§l狐疑之神帆楼", "tennogamenolife.race.horou.description": "狐疑之神帆楼是世界原初的神明，具有无与伦比的力量和魔素值。因其孤独而创造了机凯种族，最终能量汇聚后重归本源，达到神级存在。", "tennogamenolife.race.horou.notes": "作为世界的创造者，拥有操控空间、时间和元素的能力，在战斗中几乎无敌。", "item.tennogamenolife.mechanical_core": "§5机械核心", "item.tennogamenolife.mechanical_core.desc": "§6§l远古遗物", "tensura.evolution_menu.mechanical_core_requirement": "消耗8个机械核心", "tennogamenolife.skill.barrier": "§l通行管制", "tennogamenolife.skill.barrier.description": "阻止生物接近你，使他们站着不动！", "tensura.evolution_menu.ep_requirement_specific": "拥有至少100万存在值", "tensura.evolution_menu.dragon_essence_requirement": "消耗10个龙之精华", "tennogamenolife.race.flugel": "天翼种", "tennogamenolife.race.flugel.notes": "由战神创造的十六种族之一，拥有天使般的翅膀，象征着死亡。", "tennogamenolife.race.higher_flugel": "上级天翼", "tennogamenolife.race.higher_flugel.notes": "天翼族的进化形态，拥有更强大的飞行能力和魔素控制，具备更加敏锐的战斗感知。", "tennogamenolife.skill.tengeki": "§6§l天击", "tennogamenolife.skill.tengeki.description": "是在天翼种在大战时使用的魔法，是她们的唯一有命名的招式，也是她们的最强攻击手段。", "skill.tengeki.forbidden_dimension": "§c此技能无法在迷宫维度使用！", "tennogamenolife.charging": "蓄力中：%d秒", "tennogamenolife.charging_complete": "蓄力增强中：%d秒", "tennogamenolife.charging_max": "最强一击充能完毕", "death.attack.magicule_explosion": "【%s】因身体承受不住而爆体而亡", "death.attack.magicule_explosion.player": "【%s】因身体承受不住而爆体而亡", "effect.tennogamenolife.weaken": "幼年状态", "tennogamenolife.race.jibril": "吉普莉尔", "tennogamenolife.race.raphael": "拉斐尔", "tennogamenolife.race.azril": "阿兹莉尔", "tennogamenolife.race.puraiya": "§6遗志体", "tennogamenolife.skill.reisen": "§6§l典开§4Rēsen", "tennogamenolife.skill.reisen.description": "复制对方的终极技能，持续较短时间", "tennogamenolife.skill.reisen.success_copy": "成功复制了 %s 技能！", "tennogamenolife.skill.reisen.success_copy_duration": "成功复制了 %s 的终极技能，持续%d秒！", "tennogamenolife.skill.reisen.cannot_copy": "复制失败，请重新尝试", "tennogamenolife.skill.reisen.no_ultimate_skill": "目标没有可复制的终极技能！", "tennogamenolife.skill.reisen.only_player_skill": "只能复制玩家的终极技能！", "tennogamenolife.skill.reisen.no_target": "没有找到目标！", "tennogamenolife.race.arutoshu": "§4§l战神阿尔特修", "block.tennogamenolife.chess_block": "§6§l国际象棋", "tennogamenolife.race.dwarf": "地精", "tennogamenolife.race.dwarf.notes": "尽管无法直接使用魔法，他们却擅长制造魔法机器。", "tennogamenolife.race.nii_tiruvirugu": "尼依・缇儿威尔古", "item.tennogamenolife.star_essence": "星之精华", "tensura.evolution_menu.star_essence_requirement": "需要消耗1个星之精华", "tennogamenolife.race.veigu_dorauvuniru": "维格・德劳乌尼尔", "tennogamenolife.race.roni_dorauvuniru": "罗尼・德劳乌尼尔", "tennogamenolife.race.ocain": "奥肯因", "item.tennogamenolife.sickle": "§6天翼之镰", "tooltip.tensura.long_sword.tooltip": "§4§l小吉的专武吗？", "tennogamenolife.race.fantasuma": "幻想种", "tennogamenolife.race.fantasuma.notes": "他们作为十六种族中的第二位，是拥有意识的天地异变。 ", "tennogamenolife.race.dragonia": "龙精种", "tennogamenolife.race.dragonia.notes": "拥有龙之血脉的强大种族，具有卓越的力量和魔法抗性。", "tennogamenolife.race.lesser_dragonia": "下级龙精种", "tennogamenolife.race.lesser_dragonia.notes": "龙精种的进化形态，拥有更强的力量和魔法抗性，以及更高的魔素值。", "tennogamenolife.race.middle_dragonia": "中阶龙精种", "tennogamenolife.race.middle_dragonia.notes": "下级龙精种的进化形态，拥有更高的生命值和魔素值，是更为强大的龙族。", "tennogamenolife.race.higher_dragonia": "上阶龙精种", "tennogamenolife.race.higher_dragonia.notes": "中阶龙精种的进化形态，拥有极高的生命值和攻击力，是龙族中的顶级存在。", "tennogamenolife.race.aranreivu": "焉龙", "tennogamenolife.race.aranreivu.notes": "上阶龙精种的最终进化形态，拥有神性的龙族存在，具备惊人的力量与生命力。", "tennogamenolife.race.hartyleif": "终龙", "tennogamenolife.race.hartyleif.notes": "上阶龙精种的另一进化形态，具有极强的毁灭力与统御能力，龙族中的至高存在。", "tennogamenolife.race.reginreivu": "聪龙", "tennogamenolife.race.reginreivu.notes": "上阶龙精种的智慧进化形态，拥有超凡的智慧与预知能力，龙族中的智者存在。", "message.tennogamenolife.damage_dealt": "§8造成伤害 §c%s §e点", "entity.tennogamenolife.hurt_dummy": "§d试验假人", "item.tennogamenolife.hurt_dummy": "§d试验人偶", "tennogamenolife.skill.chain_mining": "破矿者", "tennogamenolife.skill.chain_mining.description": "你就当个地鼠吧，挖遍天下矿脉。", "skill.tennogamenolife.chain_mining.enabled": "破矿者已启用", "skill.tennogamenolife.chain_mining.disabled": "破矿者已禁用", "skill.tennogamenolife.chain_mining.no_magicule": "魔素不足，无法进行破矿", "skill.tennogamenolife.chain_mining.mastered": "破矿者技能已熟练！最大连锁数量提升至24个方块。", "skill.tennogamenolife.chain_mining.mode.chain": "破矿者[默认]", "skill.tennogamenolife.chain_mining.mode.horizontal": "平面挖掘", "skill.tennogamenolife.chain_mining.mode.vertical": "垂直挖掘", "skill.tennogamenolife.chain_mining.mode.switch": "已切换到%s模式", "skill.tennogamenolife.chain_mining.mode.locked": "技能未熟练，无法切换模式", "tennogamenolife.skill.chain_felling": "伐之者", "tennogamenolife.skill.chain_felling.description": "砍树砍树，想致富先砍树。", "skill.tennogamenolife.chain_felling.enabled": "伐之者已启用", "skill.tennogamenolife.chain_felling.disabled": "伐之者已禁用", "skill.tennogamenolife.chain_felling.no_magicule": "魔素不足，无法进行伐之", "skill.tennogamenolife.chain_felling.mastered": "伐之者技能已熟练！最大连锁数量提升至64个方块。", "tennogamenolife.skill.chain_shovel": "掘土者", "tennogamenolife.skill.chain_shovel.description": "铲平一切障碍，挖出一座城池。", "skill.tennogamenolife.chain_shovel.enabled": "掘土者已启用", "skill.tennogamenolife.chain_shovel.disabled": "掘土者已禁用", "skill.tennogamenolife.chain_shovel.no_magicule": "魔素不足，无法进行掘土", "skill.tennogamenolife.chain_shovel.mastered": "掘土者技能已熟练！最大连锁数量提升至24个方块。", "skill.tennogamenolife.chain_shovel.mode.locked": "技能未熟练，无法切换模式", "skill.tennogamenolife.chain_shovel.mode.switch": "已切换到%s模式", "skill.tennogamenolife.chain_shovel.mode.chain": "掘土者[默认]", "skill.tennogamenolife.chain_shovel.mode.horizontal": "平面挖掘", "skill.tennogamenolife.chain_shovel.mode.vertical": "垂直挖掘", "item.tennogamenolife.chain_mining_skill_book": "破矿者技能书", "item.tennogamenolife.chain_mining_skill_book.tooltip": "右键使用可获得破矿者技能", "item.tennogamenolife.chain_mining_skill_book.tooltip2": "连锁挖掘让你一下子挖取多个相同的方块", "item.tennogamenolife.chain_mining_skill_book.learned": "成功学习了破矿者技能！", "item.tennogamenolife.chain_mining_skill_book.already_learned": "你已经拥有破矿者技能了！", "item.tennogamenolife.chain_felling_skill_book": "伐之者技能书", "item.tennogamenolife.chain_felling_skill_book.tooltip": "右键使用可获得伐之者技能", "item.tennogamenolife.chain_felling_skill_book.tooltip2": "连锁砍伐让你一次砍倒整棵树", "item.tennogamenolife.chain_felling_skill_book.learned": "成功学习了伐之者技能！", "item.tennogamenolife.chain_felling_skill_book.already_learned": "你已经拥有伐之者技能了！", "item.tennogamenolife.chain_shovel_skill_book": "掘土者技能书", "item.tennogamenolife.chain_shovel_skill_book.tooltip": "右键使用可获得掘土者技能", "item.tennogamenolife.chain_shovel_skill_book.tooltip2": "连锁铲除让你一下子铲平大片土地", "item.tennogamenolife.chain_shovel_skill_book.learned": "成功学习了掘土者技能！", "item.tennogamenolife.chain_shovel_skill_book.already_learned": "你已经拥有掘土者技能了！"}