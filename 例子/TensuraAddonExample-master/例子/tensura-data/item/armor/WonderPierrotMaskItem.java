package com.github.manasmods.tensura.item.armor;

import com.github.manasmods.tensura.item.TensuraArmourMaterials;
import com.github.manasmods.tensura.item.TensuraCreativeTab;
import com.github.manasmods.tensura.util.TensuraRarity;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.monster.EnderMan;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Item.Properties;
import net.minecraft.world.level.Level;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.item.GeoArmorItem;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class WonderPierrotMaskItem extends GeoArmorItem implements IAnimatable {
   private final AnimationFactory factory = GeckoLibUtil.createFactory(this);

   public WonderPierrotMaskItem() {
      super(TensuraArmourMaterials.WONDER_PIERROT_MASK, EquipmentSlot.HEAD, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR).m_41497_(TensuraRarity.UNIQUE));
   }

   public void onArmorTick(ItemStack pStack, Level pLevel, Player player) {
      if (!pLevel.m_5776_() && player.m_21023_(MobEffects.f_216964_)) {
         player.m_21195_(MobEffects.f_216964_);
      }

   }

   public boolean isEnderMask(ItemStack stack, Player player, EnderMan enderManEntity) {
      return true;
   }

   public void registerControllers(AnimationData data) {
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }
}
