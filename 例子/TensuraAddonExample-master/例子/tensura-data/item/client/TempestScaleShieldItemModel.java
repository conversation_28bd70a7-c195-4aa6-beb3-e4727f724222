package com.github.manasmods.tensura.item.client;

import com.github.manasmods.tensura.item.custom.TempestScaleShieldItem;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.model.AnimatedGeoModel;

public class TempestScaleShieldItemModel extends AnimatedGeoModel<TempestScaleShieldItem> {
   public ResourceLocation getModelResource(TempestScaleShieldItem object) {
      return new ResourceLocation("tensura", "geo/items/tempest_scale_shield.geo.json");
   }

   public ResourceLocation getTextureResource(TempestScaleShieldItem object) {
      return new ResourceLocation("tensura", "textures/item/tempest_scale_shield_model.png");
   }

   public ResourceLocation getAnimationResource(TempestScaleShieldItem animatable) {
      return null;
   }
}
