package com.github.manasmods.tensura.entity.human;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.tensura.registry.sound.TensuraSoundEvents;
import java.util.List;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.Level;

public class OtherworlderEntity extends PlayerLikeEntity implements IOtherworlder {
   public OtherworlderEntity(EntityType<? extends OtherworlderEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.f_21364_ = 100;
      this.f_19793_ = 1.0F;
   }

   public ResourceLocation getTextureLocation() {
      return null;
   }

   public List<ManasSkill> getUniqueSkills() {
      return List.of();
   }

   protected boolean removeWhenNoAction() {
      return false;
   }

   public InteractionResult handleEating(Player player, InteractionHand hand, ItemStack stack) {
      if (this.m_6898_(stack) && this.m_21223_() < this.m_21233_()) {
         if (!player.m_7500_()) {
            stack.m_41774_(1);
         }

         this.m_8035_();
         this.m_9236_().m_6269_((Player)null, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
         return InteractionResult.SUCCESS;
      } else {
         return InteractionResult.PASS;
      }
   }

   public void m_8035_() {
      super.m_8035_();
      this.m_5634_(3.0F);
   }

   public void m_6667_(DamageSource source) {
      super.m_6667_(source);
      if (!this.f_19853_.m_5776_()) {
         if (!this.m_6084_()) {
            this.dropSkills(source.m_7639_());
         }
      }
   }
}
