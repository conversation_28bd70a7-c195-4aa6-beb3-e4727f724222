package com.github.manasmods.tensura.item.custom;

import com.github.manasmods.tensura.item.TensuraCreativeTab;
import com.github.manasmods.tensura.util.TensuraRarity;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.Item.Properties;

public class ElementCoreItem extends Item {
   private final int elemental;

   public ElementCoreItem() {
      this(-1);
   }

   public ElementCoreItem(int elemental) {
      super((new Properties()).m_41497_(TensuraRarity.UNIQUE).m_41491_(TensuraCreativeTab.MISCELLANEOUS).m_41486_().m_41499_(500));
      this.elemental = elemental;
   }

   public int getElemental() {
      return this.elemental;
   }
}
