package com.github.manasmods.tensura.event;

import net.minecraft.world.entity.LivingEntity;
import net.minecraftforge.eventbus.api.Cancelable;
import net.minecraftforge.eventbus.api.Event;

@Cancelable
public class EnergyRegenerateTickEvent extends Event {
   private LivingEntity entity;

   public EnergyRegenerateTickEvent(LivingEntity entity) {
      this.entity = entity;
   }

   public LivingEntity getEntity() {
      return this.entity;
   }

   public void setEntity(LivingEntity entity) {
      this.entity = entity;
   }

   public static class Aura extends EnergyRegenerateTickEvent {
      private final double baseValue;
      private final double originalValue;
      private final double maxAP;
      private double value;

      public Aura(LivingEntity entity, double baseValue, double originalValue, double maxMP) {
         super(entity);
         this.baseValue = baseValue;
         this.originalValue = originalValue;
         this.value = originalValue;
         this.maxAP = maxMP;
      }

      public double getBaseValue() {
         return this.baseValue;
      }

      public double getOriginalValue() {
         return this.originalValue;
      }

      public double getMaxAP() {
         return this.maxAP;
      }

      public double getValue() {
         return this.value;
      }

      public void setValue(double value) {
         this.value = value;
      }
   }

   public static class Magicule extends EnergyRegenerateTickEvent {
      private final double baseValue;
      private final double originalValue;
      private final double maxMP;
      private double value;

      public Magicule(LivingEntity entity, double baseValue, double originalValue, double maxMP) {
         super(entity);
         this.baseValue = baseValue;
         this.originalValue = originalValue;
         this.value = originalValue;
         this.maxMP = maxMP;
      }

      public double getBaseValue() {
         return this.baseValue;
      }

      public double getOriginalValue() {
         return this.originalValue;
      }

      public double getMaxMP() {
         return this.maxMP;
      }

      public double getValue() {
         return this.value;
      }

      public void setValue(double value) {
         this.value = value;
      }
   }
}
