package com.github.manasmods.tensura.client.screen;

import com.github.manasmods.manascore.api.client.gui.FontRenderHelper;
import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.menu.RaceSelectionMenu;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.race.RaceHelper;
import com.mojang.blaze3d.platform.InputConstants;
import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.PoseStack;
import java.awt.Color;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import net.minecraft.ChatFormatting;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.screens.inventory.AbstractContainerScreen;
import net.minecraft.client.gui.screens.inventory.InventoryScreen;
import net.minecraft.client.renderer.GameRenderer;
import net.minecraft.client.resources.sounds.SimpleSoundInstance;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.HoverEvent;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.HoverEvent.Action;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.entity.player.Inventory;

public class RaceSelectionScreen extends AbstractContainerScreen<RaceSelectionMenu> {
   private static final ResourceLocation BACKGROUND = new ResourceLocation("tensura", "textures/gui/raceselector/gui.png");
   private final MutableComponent submitButtonText = Component.m_237115_("tensura.race.selection.submit");
   private int prevButtonX = 0;
   private int prevButtonY = 0;
   private int nextButtonX = 0;
   private int nextButtonY = 0;
   private int submitButtonX = 0;
   private int submitButtonY = 0;
   private int randomIndex = 0;
   private int scale;
   private int tick = 0;

   public RaceSelectionScreen(RaceSelectionMenu pMenu, Inventory pPlayerInventory, Component pTitle) {
      super(pMenu, pPlayerInventory, pTitle.m_6881_().m_130940_(ChatFormatting.WHITE));
   }

   protected void m_7856_() {
      super.m_7856_();
      int var10001 = -this.getGuiTop();
      Objects.requireNonNull(this.f_96547_);
      this.f_97729_ = var10001 - 9 - 1;
      var10001 = -this.getGuiTop();
      Objects.requireNonNull(this.f_96547_);
      this.f_97731_ = var10001 - 9 - 1;
      this.prevButtonX = this.getGuiLeft() + 7;
      this.prevButtonY = this.getGuiTop() + 6;
      this.nextButtonX = this.getGuiLeft() + this.f_97726_ - 18 - 6;
      this.nextButtonY = this.getGuiTop() + 6;
      this.submitButtonX = this.getGuiLeft() + 7;
      this.submitButtonY = this.getGuiTop() + this.f_97727_ - 19;
      this.scale = Math.round(30.0F * RaceHelper.getRaceSize(this.getSelectedRace()));
   }

   protected void m_7286_(PoseStack pPoseStack, float pPartialTick, int pMouseX, int pMouseY) {
      this.m_7333_(pPoseStack);
      RenderSystem.m_157427_(GameRenderer::m_172817_);
      RenderSystem.m_157429_(1.0F, 1.0F, 1.0F, 1.0F);
      RenderSystem.m_157456_(0, BACKGROUND);
      int x = (this.f_96543_ - this.f_97726_) / 2;
      int y = (this.f_96544_ - this.f_97727_) / 2;
      this.m_93228_(pPoseStack, x, y, 0, 0, this.f_97726_, this.f_97727_);
      this.renderButtons(pPoseStack, pMouseX, pMouseY);
      this.renderRace(pPoseStack, pMouseX, pMouseY);
      if (((RaceSelectionMenu)this.f_97732_).selectedRaceIndex.m_6501_() == ((RaceSelectionMenu)this.f_97732_).registeredRaces.size() - 1) {
         List<Float> size = Arrays.asList(0.5F, 0.75F, 1.0F, 1.25F, 1.5F, 1.75F, 2.0F, 2.25F, 2.5F, 2.75F, 3.0F);
         this.scale = Math.round(30.0F * ((Float)size.get(this.randomIndex) / 2.0F));
         ++this.tick;
         if (this.tick % 40 == 0) {
            this.randomIndex = this.f_96541_.f_91074_.m_217043_().m_188503_(size.size());
         }
      }

      InventoryScreen.m_98850_(this.getGuiLeft() + 38, this.getGuiTop() + 140, this.scale, (float)(this.getGuiLeft() + 38 - pMouseX), (float)(this.getGuiTop() + 140) - (float)(50 * this.scale) / 30.0F - (float)pMouseY, this.f_96541_.f_91074_);
   }

   protected void renderRace(PoseStack pPoseStack, int pMouseX, int pMouseY) {
      if (((RaceSelectionMenu)this.f_97732_).selectedRaceIndex.m_6501_() != ((RaceSelectionMenu)this.f_97732_).registeredRaces.size() - 1) {
         this.f_96547_.m_92889_(pPoseStack, ((MutableComponent)Objects.requireNonNull(this.getSelectedRace().getName())).m_130940_(ChatFormatting.BOLD), (float)(this.getXSize() - this.f_96547_.m_92852_(this.getSelectedRace().getName())) / 2.0F + (float)this.getGuiLeft(), (float)(this.getGuiTop() + 7), Color.WHITE.getRGB());
         FontRenderHelper.renderScaledTextInArea(pPoseStack, this.f_96547_, Component.m_237110_("tensura.race.selection.infobox.1", new Object[]{this.getSelectedRace().getDifficulty().asText(), ((Double)this.getSelectedRace().getBaseAuraRange().getFirst()).longValue(), ((Double)this.getSelectedRace().getBaseAuraRange().getSecond()).longValue(), ((Double)this.getSelectedRace().getBaseMagiculeRange().getFirst()).longValue(), ((Double)this.getSelectedRace().getBaseMagiculeRange().getSecond()).longValue()}), (float)(this.getGuiLeft() + this.getXSize() - 95), (float)(this.getGuiTop() + 26), 91.0F, 35.0F, Color.WHITE, 4.0F);
         MutableComponent intrinsicSkill = Component.m_237119_();
         List<TensuraSkill> list = this.getSelectedRace().getIntrinsicSkills(((RaceSelectionMenu)this.f_97732_).getPlayer());
         if (list.isEmpty()) {
            intrinsicSkill = Component.m_237115_("tensura.race.selection.skills.empty").m_130940_(ChatFormatting.GRAY);
         } else {
            for(int i = 0; i < list.size(); ++i) {
               TensuraSkill tensuraSkill = (TensuraSkill)list.get(i);
               if (tensuraSkill instanceof Skill) {
                  Skill skill = (Skill)tensuraSkill;
                  intrinsicSkill.m_7220_(((MutableComponent)Objects.requireNonNull(skill.getName())).m_130938_((style) -> {
                     return style.m_131157_(skill.getType().getChatFormatting()).m_131144_(new HoverEvent(Action.f_130831_, skill.getType().getName()));
                  }));
               }

               if (i + 1 != list.size()) {
                  intrinsicSkill.m_130946_(", ");
               }
            }
         }

         FontRenderHelper.renderScaledTextInArea(pPoseStack, this.f_96547_, Component.m_237110_("tensura.race.selection.infobox.2", new Object[]{intrinsicSkill}), (float)(this.getGuiLeft() + this.getXSize() - 95), (float)(this.getGuiTop() + 60), 90.0F, 35.0F, Color.WHITE, 4.0F);
         FontRenderHelper.renderScaledTextInArea(pPoseStack, this.f_96547_, Component.m_237115_(this.getSelectedRace().getNameTranslationKey() + ".notes"), (float)(this.getGuiLeft() + this.getXSize() - 95), (float)(this.getGuiTop() + 96), 90.0F, 70.0F, Color.WHITE, 3.0F);
      } else {
         this.f_96547_.m_92889_(pPoseStack, Component.m_237115_("tensura.race.selection.random").m_130940_(ChatFormatting.BOLD), (float)(this.getXSize() - this.f_96547_.m_92852_(Component.m_237115_("tensura.race.selection.random"))) / 2.0F + (float)this.getGuiLeft(), (float)(this.getGuiTop() + 7), Color.WHITE.getRGB());
         FontRenderHelper.renderScaledTextInArea(pPoseStack, this.f_96547_, Component.m_237110_("tensura.race.selection.infobox.1", new Object[]{Component.m_237115_("tensura.race.selection.unknown").m_130940_(ChatFormatting.AQUA), "???", "???", "???", "???"}), (float)(this.getGuiLeft() + this.getXSize() - 95), (float)(this.getGuiTop() + 26), 91.0F, 35.0F, Color.WHITE, 4.0F);
         FontRenderHelper.renderScaledTextInArea(pPoseStack, this.f_96547_, Component.m_237110_("tensura.race.selection.infobox.2", new Object[]{Component.m_237115_("tensura.race.selection.unknown").m_130940_(ChatFormatting.GRAY)}), (float)(this.getGuiLeft() + this.getXSize() - 95), (float)(this.getGuiTop() + 60), 90.0F, 35.0F, Color.WHITE, 4.0F);
         FontRenderHelper.renderScaledTextInArea(pPoseStack, this.f_96547_, Component.m_237115_("tensura.race.selection.random_race"), (float)(this.getGuiLeft() + this.getXSize() - 95), (float)(this.getGuiTop() + 96), 90.0F, 70.0F, Color.WHITE, 3.0F);
      }

   }

   protected void renderButtons(PoseStack pPoseStack, int pMouseX, int pMouseY) {
      int UPrefOffset = this.mouseOverPrevButton((double)pMouseX, (double)pMouseY) ? 25 : 2;
      this.m_93228_(pPoseStack, this.prevButtonX, this.prevButtonY, UPrefOffset, 181, 18, 10);
      int NextUOffset = this.mouseOverNextButton((double)pMouseX, (double)pMouseY) ? 25 : 2;
      this.m_93228_(pPoseStack, this.nextButtonX, this.nextButtonY, NextUOffset, 168, 18, 10);
      int submitVOffset = this.mouseOverSubmitButton((double)pMouseX, (double)pMouseY) ? 183 : 168;
      this.m_93228_(pPoseStack, this.submitButtonX, this.submitButtonY, 45, submitVOffset, 63, 12);
      this.f_96547_.m_92889_(pPoseStack, this.submitButtonText, (float)(63 - this.f_96547_.m_92852_(this.submitButtonText)) / 2.0F + (float)this.submitButtonX, (float)(this.submitButtonY + 2), Color.WHITE.getRGB());
   }

   public boolean m_6375_(double pMouseX, double pMouseY, int pButton) {
      int id;
      if (this.mouseOverNextButton(pMouseX, pMouseY) && pButton == 0) {
         id = this.hasNextRace() ? ((RaceSelectionMenu)this.f_97732_).selectedRaceIndex.m_6501_() + 1 : 0;
         if (((RaceSelectionMenu)this.f_97732_).m_6366_(this.f_96541_.f_91074_, id)) {
            Minecraft.m_91087_().m_91106_().m_120367_(SimpleSoundInstance.m_119752_(SoundEvents.f_12490_, 1.0F));
            this.f_96541_.f_91072_.m_105208_(((RaceSelectionMenu)this.f_97732_).f_38840_, id);
            this.scale = Math.round(30.0F * RaceHelper.getRaceSize(this.getSelectedRace()));
         }

         return true;
      } else if (this.mouseOverPrevButton(pMouseX, pMouseY) && pButton == 0) {
         id = this.hasPrevRace() ? ((RaceSelectionMenu)this.f_97732_).selectedRaceIndex.m_6501_() - 1 : ((RaceSelectionMenu)this.f_97732_).registeredRaces.size() - 1;
         if (((RaceSelectionMenu)this.f_97732_).m_6366_(this.f_96541_.f_91074_, id)) {
            Minecraft.m_91087_().m_91106_().m_120367_(SimpleSoundInstance.m_119752_(SoundEvents.f_12490_, 1.0F));
            this.f_96541_.f_91072_.m_105208_(((RaceSelectionMenu)this.f_97732_).f_38840_, id);
            this.scale = Math.round(30.0F * RaceHelper.getRaceSize(this.getSelectedRace()));
         }

         return true;
      } else {
         id = ((RaceSelectionMenu)this.f_97732_).isRaceOnly() ? -2 : -1;
         if (this.mouseOverSubmitButton(pMouseX, pMouseY) && pButton == 0 && ((RaceSelectionMenu)this.f_97732_).m_6366_(this.f_96541_.f_91074_, id)) {
            Minecraft.m_91087_().m_91106_().m_120367_(SimpleSoundInstance.m_119752_(SoundEvents.f_12490_, 1.0F));
            this.f_96541_.f_91072_.m_105208_(((RaceSelectionMenu)this.f_97732_).f_38840_, id);
            this.m_7379_();
         }

         return super.m_6375_(pMouseX, pMouseY, pButton);
      }
   }

   protected boolean mouseOverPrevButton(double pMouseX, double pMouseY) {
      if (pMouseX < (double)this.prevButtonX) {
         return false;
      } else if (pMouseX > (double)(this.prevButtonX + 18)) {
         return false;
      } else if (pMouseY < (double)this.prevButtonY) {
         return false;
      } else {
         return pMouseY <= (double)(this.prevButtonY + 10);
      }
   }

   protected boolean mouseOverNextButton(double pMouseX, double pMouseY) {
      if (pMouseX < (double)this.nextButtonX) {
         return false;
      } else if (pMouseX > (double)(this.nextButtonX + 18)) {
         return false;
      } else if (pMouseY < (double)this.nextButtonY) {
         return false;
      } else {
         return pMouseY <= (double)(this.nextButtonY + 10);
      }
   }

   protected boolean mouseOverSubmitButton(double pMouseX, double pMouseY) {
      if (pMouseX < (double)this.submitButtonX) {
         return false;
      } else if (pMouseX > (double)(this.submitButtonX + 63)) {
         return false;
      } else if (pMouseY < (double)this.submitButtonY) {
         return false;
      } else {
         return pMouseY <= (double)(this.submitButtonY + 12);
      }
   }

   public boolean m_6913_() {
      return false;
   }

   public boolean m_7933_(int pKeyCode, int pScanCode, int pModifiers) {
      return this.f_96541_ != null && this.f_96541_.f_91066_.f_92092_.isActiveAndMatches(InputConstants.m_84827_(pKeyCode, pScanCode)) ? true : super.m_7933_(pKeyCode, pScanCode, pModifiers);
   }

   protected boolean hasPrevRace() {
      return ((RaceSelectionMenu)this.f_97732_).selectedRaceIndex.m_6501_() - 1 >= 0 && ((RaceSelectionMenu)this.f_97732_).selectedRaceIndex.m_6501_() < ((RaceSelectionMenu)this.f_97732_).registeredRaces.size();
   }

   protected boolean hasNextRace() {
      return ((RaceSelectionMenu)this.f_97732_).selectedRaceIndex.m_6501_() + 1 < ((RaceSelectionMenu)this.f_97732_).registeredRaces.size();
   }

   protected Race getSelectedRace() {
      return (Race)((RaceSelectionMenu)this.f_97732_).registeredRaces.get(((RaceSelectionMenu)this.f_97732_).selectedRaceIndex.m_6501_());
   }
}
