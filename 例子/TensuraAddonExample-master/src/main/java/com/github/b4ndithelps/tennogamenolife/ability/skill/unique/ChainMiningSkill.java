package com.github.b4ndithelps.tennogamenolife.ability.skill.unique;

import com.github.b4ndithelps.tennogamenolife.Tenreincarnation;
import com.github.b4ndithelps.tennogamenolife.registry.skill.TenreincarnationSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import net.minecraft.client.Minecraft;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.PickaxeItem;
import net.minecraft.world.item.enchantment.EnchantmentHelper;
import net.minecraft.world.item.enchantment.Enchantments;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.HitResult;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;
import net.minecraftforge.client.event.RenderLevelStageEvent;
import net.minecraftforge.event.TickEvent;
import net.minecraftforge.event.level.BlockEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraft.network.chat.Component;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.LevelRenderer;
import com.mojang.blaze3d.systems.RenderSystem;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 连锁挖矿独特技能
 */
@Mod.EventBusSubscriber(modid = Tenreincarnation.MODID)
public class ChainMiningSkill extends Skill {

    // 基础配置
    private final double magiculeCost = 100.0; // 每次连锁挖矿消耗的魔素
    
    // 技能模式枚举
    public enum MiningMode {
        CHAIN("chain"), // 默认连锁模式
        HORIZONTAL("horizontal"), // 平挖模式 
        VERTICAL("vertical"); // 垂直挖模式
        
        private final String id;
        
        MiningMode(String id) {
            this.id = id;
        }
        
        public String getId() {
            return id;
        }
        
        public String getTranslationKey() {
            return "skill.tennogamenolife.chain_mining.mode." + id;
        }
    }

    // 技能激活状态 - 改为静态，这样可以直接通过类访问
    private static boolean isActive = false;
    
    // 存储将被连锁挖掘的方块位置，用于渲染预览
    private static Set<BlockPos> highlightedBlocks = new HashSet<>();

    public ChainMiningSkill() {
        super(SkillType.UNIQUE);
    }

    @Override
    public ResourceLocation getSkillIcon() {
        return new ResourceLocation(Tenreincarnation.MODID, "textures/skill/unique/chain_mining.png");
    }

    @Override
    public boolean meetEPRequirement(Player entity, double curEP) {
        return false; // 无EP要求
    }

    @Override
    public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
        return magiculeCost;
    }
    
    /**
     * 获取技能模式数量
     */
    @Override
    public int modes() {
        return 3; // 三种模式: 连锁、水平、垂直
    }
    
    /**
     * 返回下一个模式的索引
     */
    @Override
    public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
        // 检查是否已熟练
        if (!instance.isMastered(entity)) {
            // 未熟练时只能使用默认模式
            return 1;
        }
        
        // 根据当前模式和反转标志决定下一个模式
        if (reverse) {
            // 反向切换
            switch (instance.getMode()) {
                case 1: return 3; // 连锁 -> 垂直
                case 2: return 1; // 水平 -> 连锁
                case 3: return 2; // 垂直 -> 水平
                default: return 1;
            }
        } else {
            // 正向切换
            switch (instance.getMode()) {
                case 1: return 2; // 连锁 -> 水平
                case 2: return 3; // 水平 -> 垂直
                case 3: return 1; // 垂直 -> 连锁
                default: return 1;
            }
        }
    }
    
    /**
     * 获取当前模式的名称
     */
    @Override
    public Component getModeName(int mode) {
        return switch (mode) {
            case 1 -> Component.translatable("skill.tennogamenolife.chain_mining.mode.chain");
            case 2 -> Component.translatable("skill.tennogamenolife.chain_mining.mode.horizontal");
            case 3 -> Component.translatable("skill.tennogamenolife.chain_mining.mode.vertical");
            default -> Component.translatable("skill.tennogamenolife.chain_mining.mode.chain");
        };
    }
    
    /**
     * 获取当前挖掘模式
     */
    private MiningMode getCurrentMode(ManasSkillInstance instance) {
        if (instance instanceof TensuraSkillInstance tensuraInstance) {
            int modeIndex = tensuraInstance.getMode();
            return switch (modeIndex) {
                case 2 -> MiningMode.HORIZONTAL;
                case 3 -> MiningMode.VERTICAL;
                default -> MiningMode.CHAIN;
            };
        }
        
        // 旧方法作为后备
        CompoundTag nbt = instance.getOrCreateTag();
        if (!nbt.contains("MiningMode")) {
            nbt.putString("MiningMode", MiningMode.CHAIN.getId());
        }
        String modeStr = nbt.getString("MiningMode");
        for (MiningMode mode : MiningMode.values()) {
            if (mode.getId().equals(modeStr)) {
                return mode;
            }
        }
        return MiningMode.CHAIN; // 默认值
    }
    
    /**
     * 设置当前挖掘模式
     */
    private void setCurrentMode(ManasSkillInstance instance, MiningMode mode) {
        if (instance instanceof TensuraSkillInstance tensuraInstance) {
            int modeIndex = switch (mode) {
                case HORIZONTAL -> 2;
                case VERTICAL -> 3;
                default -> 1;
            };
            tensuraInstance.setMode(modeIndex);
        } else {
            // 旧方法作为后备
            CompoundTag nbt = instance.getOrCreateTag();
            nbt.putString("MiningMode", mode.getId());
            instance.markDirty();
        }
    }

    /**
     * 当技能按键被按下时触发
     */
    @Override
    public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
        if (entity instanceof Player player) {
            // 切换技能激活状态
            isActive = !isActive;
            
            // 清除高亮方块
            highlightedBlocks.clear();
            
            // 播放音效
            Level level = entity.getLevel();
            level.playSound(
                null, entity.getX(), entity.getY(), entity.getZ(),
                isActive ? SoundEvents.BEACON_ACTIVATE : SoundEvents.BEACON_DEACTIVATE,
                SoundSource.PLAYERS, 0.6F, 1.0F
            );
            
            // 通知玩家
            if (player instanceof ServerPlayer) {
                String messageKey = isActive ? "skill.tennogamenolife.chain_mining.enabled" : "skill.tennogamenolife.chain_mining.disabled";
                player.displayClientMessage(Component.translatable(messageKey), true);
            }
        }
    }

    /**
     * 静态方法，处理方块破坏事件
     * 这样可以不通过实例直接调用
     */
    @SubscribeEvent
    public static void onBlockBreak(BlockEvent.BreakEvent event) {
        // 获取玩家
        Player player = event.getPlayer();
        if (player == null) return;
        
        // 检查技能是否激活
        if (!isActive) return;
        
        // 检查玩家手中的工具是否是镐子
        ItemStack mainHandItem = player.getMainHandItem();
        if (!(mainHandItem.getItem() instanceof PickaxeItem)) return;
        
        // 获取当前破坏的方块信息
        BlockPos pos = event.getPos();
        Level level = player.getLevel();
        BlockState targetState = event.getState();
        Block targetBlock = targetState.getBlock();
        
        // 检查方块是否适合用镐子挖掘
        if (!isMineable(targetState, mainHandItem)) return;
        
        // 获取技能实例
        ChainMiningSkill skill = (ChainMiningSkill) TenreincarnationSkill.CHAIN_MINING.get();
        
        // 尝试获取玩家的技能实例
        SkillAPI.getSkillsFrom(player).getSkill(skill).ifPresent(instance -> {
            // 如果魔素不足，不执行连锁挖矿
            if (SkillHelper.outOfMagicule(player, instance)) {
                if (player instanceof ServerPlayer) {
                    player.displayClientMessage(Component.translatable("skill.tennogamenolife.chain_mining.no_magicule"), true);
                }
                return;
            }
            
            // 根据当前模式执行不同的挖掘行为
            MiningMode currentMode = skill.getCurrentMode(instance);
            Set<BlockPos> blocksToBreak = new HashSet<>();
            
            // 获取玩家视角方向
            Direction hitFace = Direction.UP; // 默认值
            
            // 尝试获取玩家瞄准的方块面
            HitResult hitResult = player.pick(20.0, 0.0F, false);
            if (hitResult instanceof BlockHitResult blockHit) {
                hitFace = blockHit.getDirection();
            }
            
            switch (currentMode) {
                case CHAIN:
                    // 连锁模式：找到所有连接的相同方块
                    int maxBlocks = instance.isMastered(player) ? 24 : 16;
                    blocksToBreak = findChainedBlocks(level, pos, targetBlock, maxBlocks);
                    break;
                case HORIZONTAL:
                    // 平面模式：根据玩家视角决定挖掘平面
                    int planeRange = instance.isMastered(player) ? 3 : 2;
                    blocksToBreak = findPlaneBlocks(level, pos, targetBlock, planeRange, hitFace);
                    break;
                case VERTICAL:
                    // 垂直挖模式：垂直方向的方块
                    int verticalRange = instance.isMastered(player) ? 5 : 3;
                    blocksToBreak = findVerticalBlocks(level, pos, targetBlock, verticalRange);
                    break;
            }
            
            // 移除起始位置（已经由原始事件处理）
            blocksToBreak.remove(pos);
            
            // 如果找到了需要挖掘的方块，消耗魔素
            if (!blocksToBreak.isEmpty()) {
                // 消耗魔素（只有真正进行了挖掘才消耗）
                instance.setCoolDown(1);
                
                // 获取镐子的附魔效果
                int fortuneLevel = EnchantmentHelper.getItemEnchantmentLevel(Enchantments.BLOCK_FORTUNE, mainHandItem);
                int silkTouchLevel = EnchantmentHelper.getItemEnchantmentLevel(Enchantments.SILK_TOUCH, mainHandItem);
                
                // 破坏找到的方块
                for (BlockPos blockPos : blocksToBreak) {
                    // 检查方块是否可以被当前工具破坏
                    BlockState state = level.getBlockState(blockPos);
                    
                    // 跳过不适合用镐子挖掘的方块
                    if (!isMineable(state, mainHandItem)) continue;
                    
                    // 使用镐子破坏方块，同时应用附魔效果
                    if (level instanceof ServerLevel serverLevel) {
                        // 使用适当的方法来模拟正确的掉落物
                        Block block = state.getBlock();
                        
                        // 应用时运或精准采集
                        if (silkTouchLevel > 0) {
                            // 精准采集 - 直接掉落方块本身
                            Block.dropResources(state, level, blockPos, null, player, mainHandItem);
                        } else {
                            // 时运 - 增加掉落数量
                            Block.dropResources(state, level, blockPos, null, player, mainHandItem);
                        }
                        
                        // 破坏方块但不生成掉落物(因为我们已经手动处理了掉落物)
                        level.removeBlock(blockPos, false);
                    }
                    
                    // 对工具造成耐久损耗
                    if (!player.isCreative()) {
                        mainHandItem.hurtAndBreak(1, player, (p) -> p.broadcastBreakEvent(player.getUsedItemHand()));
                        
                        // 如果工具损坏了，终止挖掘
                        if (mainHandItem.isEmpty()) break;
                    }
                }
                
                // 播放挖掘成功的声音
                level.playSound(null, player.getX(), player.getY(), player.getZ(), 
                    SoundEvents.STONE_BREAK, SoundSource.BLOCKS, 0.5F, 0.8F);
                
                // 增加技能熟练度
                if (!player.isCreative()) {
                    // 熟练度增加量 = 基础值1 + 额外连锁方块数量 / 10（向下取整，最小为1）
                    int masteryToAdd = 1 + Math.max(1, blocksToBreak.size() / 10);
                    addSkillMasteryPoints(instance, player, masteryToAdd);
                }
            }
        });
    }

    /**
     * 检查方块是否适合用镐子挖掘
     */
    private static boolean isMineable(BlockState state, ItemStack tool) {
        // 检查方块是否适合用镐子挖掘
        float destroySpeed = state.getDestroySpeed(null, BlockPos.ZERO);
        
        // 方块硬度为-1表示无法破坏
        if (destroySpeed < 0) return false;
        
        // 使用标签系统检查方块是否属于矿物类型
        return state.is(net.minecraft.tags.BlockTags.MINEABLE_WITH_PICKAXE) || 
               // 对于其他情况，尝试获取方块破坏速度
               tool.getItem().getDestroySpeed(tool, state) > 1.0F;
    }
    
    /**
     * 增加技能熟练度（静态方法版本）
     */
    private static void addSkillMasteryPoints(ManasSkillInstance instance, LivingEntity entity, int amount) {
        // 获取当前和最大熟练度
        int currentMastery = instance.getMastery();
        int maxMastery = instance.getMaxMastery();
        
        // 如果已经达到满级，不再增加
        if (currentMastery >= maxMastery) {
            return;
        }
        
        // 计算新熟练度
        int newMastery = Math.min(currentMastery + amount, maxMastery);
        instance.setMastery(newMastery);
        
        // 如果刚达到熟练，通知玩家
        if (newMastery >= maxMastery && entity instanceof Player player) {
            player.displayClientMessage(Component.translatable("skill.tennogamenolife.chain_mining.mastered"), true);
            
            // 播放熟练达成音效
            entity.getLevel().playSound(null, entity.getX(), entity.getY(), entity.getZ(),
                SoundEvents.PLAYER_LEVELUP, SoundSource.PLAYERS, 0.75F, 1.0F);
        }
    }

    /**
     * 查找连锁的相同类型方块
     */
    private static Set<BlockPos> findChainedBlocks(Level level, BlockPos startPos, Block targetBlock, int maxBlocks) {
        Set<BlockPos> result = new HashSet<>();
        Set<BlockPos> visited = new HashSet<>();
        List<BlockPos> queue = new ArrayList<>();
        
        // 初始化队列
        queue.add(startPos);
        visited.add(startPos);
        
        // 广度优先搜索相邻的相同方块
        while (!queue.isEmpty() && result.size() < maxBlocks) {
            BlockPos current = queue.remove(0);
            
            // 如果是相同类型的方块，加入结果集
            if (level.getBlockState(current).getBlock() == targetBlock) {
                result.add(current);
                
                // 检查周围1格范围内的方块
                for (int x = -1; x <= 1; x++) {
                    for (int y = -1; y <= 1; y++) {
                        for (int z = -1; z <= 1; z++) {
                            // 跳过自身
                            if (x == 0 && y == 0 && z == 0) continue;
                            
                            BlockPos neighbor = current.offset(x, y, z);
                            
                            // 如果未访问过，加入队列
                            if (!visited.contains(neighbor)) {
                                visited.add(neighbor);
                                
                                // 检查是否是相同类型的方块
                                if (level.getBlockState(neighbor).getBlock() == targetBlock) {
                                    queue.add(neighbor);
                                }
                            }
                        }
                    }
                }
            }
        }
        
        return result;
    }
    
    /**
     * 查找平面上的相同类型方块
     * 根据玩家视角决定挖掘平面
     */
    private static Set<BlockPos> findPlaneBlocks(Level level, BlockPos startPos, Block targetBlock, int range, Direction hitFace) {
        Set<BlockPos> result = new HashSet<>();
        
        // 添加中心方块到结果集
        result.add(startPos);
        
        // 根据击中的面确定平面
        Direction.Axis excludedAxis;
        
        // 确定哪个轴应该被排除(即垂直于平面的轴)
        if (hitFace.getAxis() == Direction.Axis.Y) {
            // 如果击中上下面，排除Y轴 - 在XZ平面(水平面)上挖掘
            excludedAxis = Direction.Axis.Y;
        } else if (hitFace.getAxis() == Direction.Axis.X) {
            // 如果击中X轴方向的面，排除X轴 - 在YZ平面上挖掘
            excludedAxis = Direction.Axis.X;
        } else {
            // 如果击中Z轴方向的面，排除Z轴 - 在XY平面上挖掘
            excludedAxis = Direction.Axis.Z;
        }
        
        // 在二维平面上搜索
        for (int dx = -range; dx <= range; dx++) {
            for (int dy = -range; dy <= range; dy++) {
                for (int dz = -range; dz <= range; dz++) {
                    // 跳过中心点
                    if (dx == 0 && dy == 0 && dz == 0) continue;
                    
                    // 确保我们在所选的平面上(即有一个坐标保持不变)
                    if (excludedAxis == Direction.Axis.X && dx != 0) continue;
                    if (excludedAxis == Direction.Axis.Y && dy != 0) continue;
                    if (excludedAxis == Direction.Axis.Z && dz != 0) continue;
                    
                    BlockPos pos = startPos.offset(dx, dy, dz);
                    
                    // 检查方块类型
                    if (level.getBlockState(pos).getBlock() == targetBlock) {
                        result.add(pos);
                    }
                }
            }
        }
        
        return result;
    }
    
    /**
     * 查找垂直线上的相同类型方块
     */
    private static Set<BlockPos> findVerticalBlocks(Level level, BlockPos startPos, Block targetBlock, int range) {
        Set<BlockPos> result = new HashSet<>();
        
        // 添加中心方块到结果集
        result.add(startPos);
        
        // 向上搜索
        for (int y = 1; y <= range; y++) {
            BlockPos pos = startPos.above(y);
            if (level.getBlockState(pos).getBlock() == targetBlock) {
                result.add(pos);
            } else {
                // 如果遇到不同类型的方块，停止搜索
                break;
            }
        }
        
        // 向下搜索
        for (int y = 1; y <= range; y++) {
            BlockPos pos = startPos.below(y);
            if (level.getBlockState(pos).getBlock() == targetBlock) {
                result.add(pos);
            } else {
                // 如果遇到不同类型的方块，停止搜索
                break;
            }
        }
        
        return result;
    }
    
    /**
     * 客户端Tick事件，用于更新高亮方块
     */
    @SubscribeEvent
    @OnlyIn(Dist.CLIENT)
    public static void onClientTick(TickEvent.ClientTickEvent event) {
        if (event.phase != TickEvent.Phase.END) return;
        
        Minecraft mc = Minecraft.getInstance();
        Player player = mc.player;
        
        // 清除旧的高亮方块
        highlightedBlocks.clear();
        
        // 如果技能未激活或玩家不存在，直接返回
        if (!isActive || player == null) return;
        
        // 检查玩家手中是否有镐子
        ItemStack mainHandItem = player.getMainHandItem();
        if (!(mainHandItem.getItem() instanceof PickaxeItem)) return;
        
        // 获取玩家看向的方块
        HitResult hitResult = mc.hitResult;
        if (hitResult == null || hitResult.getType() != HitResult.Type.BLOCK) return;
        
        BlockHitResult blockHit = (BlockHitResult) hitResult;
        BlockPos hitPos = blockHit.getBlockPos();
        Level level = player.getLevel();
        BlockState hitState = level.getBlockState(hitPos);
        
        // 如果玩家看向的方块适合用镐子挖掘
        if (isMineable(hitState, mainHandItem)) {
            Block targetBlock = hitState.getBlock();
            
            // 预览挖掘的方块
            ChainMiningSkill skill = (ChainMiningSkill) TenreincarnationSkill.CHAIN_MINING.get();
            SkillAPI.getSkillsFrom(player).getSkill(skill).ifPresent(instance -> {
                // 根据当前模式预览不同的挖掘效果
                MiningMode currentMode = skill.getCurrentMode(instance);
                
                // 获取玩家瞄准的方块面
                Direction hitFace = blockHit.getDirection();
                
                switch (currentMode) {
                    case CHAIN:
                        int chainMaxBlocks = instance.isMastered(player) ? 24 : 16;
                        highlightedBlocks = findChainedBlocks(level, hitPos, targetBlock, chainMaxBlocks);
                        break;
                    case HORIZONTAL:
                        int planeRange = instance.isMastered(player) ? 3 : 2;
                        highlightedBlocks = findPlaneBlocks(level, hitPos, targetBlock, planeRange, hitFace);
                        break;
                    case VERTICAL:
                        int verticalRange = instance.isMastered(player) ? 5 : 3;
                        highlightedBlocks = findVerticalBlocks(level, hitPos, targetBlock, verticalRange);
                        break;
                }
            });
        }
    }
    
    /**
     * 渲染事件，用于高亮连锁挖掘的方块
     */
    @SubscribeEvent
    @OnlyIn(Dist.CLIENT)
    public static void onRenderLevel(RenderLevelStageEvent event) {
        if (event.getStage() != RenderLevelStageEvent.Stage.AFTER_TRANSLUCENT_BLOCKS) return;
        if (highlightedBlocks.isEmpty()) return;
        
        Minecraft mc = Minecraft.getInstance();
        PoseStack poseStack = event.getPoseStack();
        
        // 获取相机位置
        double cameraX = mc.gameRenderer.getMainCamera().getPosition().x;
        double cameraY = mc.gameRenderer.getMainCamera().getPosition().y;
        double cameraZ = mc.gameRenderer.getMainCamera().getPosition().z;
        
        // 设置渲染状态
        RenderSystem.enableBlend();
        RenderSystem.defaultBlendFunc();
        RenderSystem.disableDepthTest();
        RenderSystem.disableTexture();
        
        // 获取顶点缓冲区
        MultiBufferSource.BufferSource bufferSource = mc.renderBuffers().bufferSource();
        VertexConsumer builder = bufferSource.getBuffer(RenderType.lines());
        
        // 渲染每个方块的轮廓
        for (BlockPos pos : highlightedBlocks) {
            poseStack.pushPose();
            poseStack.translate(pos.getX() - cameraX, pos.getY() - cameraY, pos.getZ() - cameraZ);
            
            // 绘制方块边缘 - 白色轮廓
            LevelRenderer.renderLineBox(
                poseStack,
                builder,
                0, 0, 0, 1, 1, 1,
                1.0f, 1.0f, 1.0f, 0.5f // RGBA: 白色，50%透明
            );
            
            poseStack.popPose();
        }
        
        // 结束渲染
        bufferSource.endBatch(RenderType.lines());
        RenderSystem.enableTexture();
        RenderSystem.enableDepthTest();
        RenderSystem.disableBlend();
    }
} 