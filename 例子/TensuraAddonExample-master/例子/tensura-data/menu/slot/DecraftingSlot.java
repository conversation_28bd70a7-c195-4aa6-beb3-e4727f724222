package com.github.manasmods.tensura.menu.slot;

import com.github.manasmods.tensura.menu.DegenerateCraftingMenu;
import net.minecraft.world.Container;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.inventory.Slot;
import net.minecraft.world.item.ItemStack;
import net.minecraftforge.event.ForgeEventFactory;
import org.jetbrains.annotations.NotNull;

public class DecraftingSlot extends Slot {
   private final DegenerateCraftingMenu menu;
   private final Player player;
   private int removeCount;

   public DecraftingSlot(Player pPlayer, DegenerateCraftingMenu menu, Container pContainer, int pSlot, int xPosition, int yPosition) {
      super(pContainer, pSlot, xPosition, yPosition);
      this.player = pPlayer;
      this.menu = menu;
   }

   public boolean m_5857_(@NotNull ItemStack stack) {
      return false;
   }

   public void m_142406_(Player pPlayer, ItemStack pStack) {
      this.m_5845_(pStack);
      this.menu.resultContainer.m_7407_(0, 1);
      this.menu.resultContainer.m_6596_();
      if (!this.player.m_150109_().m_36054_(this.m_7993_())) {
         this.player.m_36176_(this.m_7993_(), false);
      }

      if (this.menu.decraftSlots.m_7983_()) {
         this.menu.craftSlots.setCanPlace(true);
      }

   }

   @NotNull
   public ItemStack m_6201_(int pAmount) {
      if (this.m_6657_()) {
         this.removeCount += Math.min(pAmount, this.m_7993_().m_41613_());
      }

      return super.m_6201_(pAmount);
   }

   protected void m_7169_(ItemStack pStack, int pAmount) {
      this.removeCount += pAmount;
      this.m_5845_(pStack);
   }

   protected void m_6405_(int pNumItemsCrafted) {
      this.removeCount += pNumItemsCrafted;
   }

   protected void m_5845_(ItemStack pStack) {
      if (this.removeCount > 0) {
         pStack.m_41678_(this.player.f_19853_, this.player, this.removeCount);
         ForgeEventFactory.firePlayerCraftingEvent(this.player, pStack, this.menu.craftSlots);
      }

      this.removeCount = 0;
   }
}
