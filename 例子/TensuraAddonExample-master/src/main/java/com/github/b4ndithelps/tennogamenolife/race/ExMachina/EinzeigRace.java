package com.github.b4ndithelps.tennogamenolife.race.ExMachina;

import com.github.b4ndithelps.tennogamenolife.registry.item.Tenreincarnationitems;
import com.github.b4ndithelps.tennogamenolife.registry.race.Tenreincarnationraces;
import com.github.b4ndithelps.tennogamenolife.registry.skill.TenreincarnationSkill;
import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.github.manasmods.tensura.util.JumpPowerHelper;
import com.mojang.datafixers.util.Pair;
import net.minecraft.network.chat.Component;
import net.minecraft.world.entity.player.Player;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.client.player.LocalPlayer;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.stats.Stats;

import java.util.ArrayList;
import java.util.List;

/**
 * 爱因兹希是指挥体的进化形态
 * 具有更高的防御力和魔素值，以及更强的空间操控能力
 */
public class EinzeigRace extends Race {

    // 种族基础属性
    private double baseHealth = 800.0;          // 基础生命值
    private double baseAttackDamage = 5.2;     // 基础攻击伤害
    private double baseAttackSpeed = 4.3;      // 基础攻击速度
    private double knockbackResistance = 0.5;   // 击退抗性
    private double jumpHeight = 1.0;           // 跳跃高度
    private double movementSpeed = 0.11;        // 移动速度
    private double sprintSpeed = 0.15;         // 冲刺速度
    private double auraMin = 2500.0;           // 最小灵气值
    private double auraMax = 3500.0;           // 最大灵气值
    private double startingMagiculeMin = 400.0; // 最小初始魔素值
    private double startingMagiculeMax = 500.0; // 最大初始魔素值

    private float playerSize = 2.0f;           // 玩家大小

    public EinzeigRace() {
        // 设置为困难种族
        super(Difficulty.HARD);
    }

    @Override
    public double getBaseHealth() {
        return baseHealth;
    }

    @Override
    public float getPlayerSize() {
        return playerSize;
    }

    @Override
    public double getBaseAttackDamage() {
        return baseAttackDamage;
    }

    @Override
    public double getBaseAttackSpeed() {
        return baseAttackSpeed;
    }

    @Override
    public double getKnockbackResistance() {
        return knockbackResistance;
    }

    @Override
    public double getJumpHeight() {
        return JumpPowerHelper.defaultPlayer(jumpHeight);
    }

    @Override
    public double getMovementSpeed() {
        return movementSpeed;
    }

    @Override
    public double getSprintSpeed() {
        return sprintSpeed;
    }

    @Override
    public Pair<Double, Double> getBaseAuraRange() {
        return Pair.of(auraMin, auraMax);
    }

    @Override
    public Pair<Double, Double> getBaseMagiculeRange() {
        return Pair.of(startingMagiculeMin, startingMagiculeMax);
    }

    @Override
    public List<TensuraSkill> getIntrinsicSkills(Player player) {
        List<TensuraSkill> list = new ArrayList<>();
        list.add(IntrinsicSkills.BODY_ARMOR.get());
        list.add(ExtraSkills.HEAVENLY_EYE.get());
        list.add(ExtraSkills.MAGIC_SENSE.get());
        list.add(ExtraSkills.THOUGHT_ACCELERATION.get());
        list.add(ResistanceSkills.COLD_RESISTANCE.get());
        list.add(ResistanceSkills.PHYSICAL_ATTACK_RESISTANCE.get());
        list.add(ResistanceSkills.MAGIC_RESISTANCE.get());
        list.add(UniqueSkills.COMMANDER.get());
        return list;
    }

    @Override
    public boolean isMajin() {
        return false;
    }

    @Override
    public boolean isSpiritual() {
        return false;
    }

    @Override
    public boolean isDivine() {
        return false;
    }

    @Override
    public void raceAbility(Player player) {
        if (!player.isSpectator() && !player.isCreative()) {
            if (player.getAbilities().mayfly) {
                // 关闭飞行
                player.getAbilities().mayfly = false;
                player.getAbilities().flying = false;
                player.onUpdateAbilities();
            } else {
                // 开启飞行
                player.getAbilities().mayfly = true;
                player.getAbilities().flying = true;
                player.onUpdateAbilities();
            }
        }
    }

    @Override
    public double getEvolutionPercentage(Player player) {
        // 获取玩家使用机械核心的次数
        double mechanicalCoreCount = 0.0;
        if (player instanceof LocalPlayer) {
            LocalPlayer localPlayer = (LocalPlayer)player;
            mechanicalCoreCount = localPlayer.getStats().getValue(Stats.ITEM_USED.get(Tenreincarnationitems.MECHANICAL_CORE.get()));
        } else if (player instanceof ServerPlayer) {
            ServerPlayer serverPlayer = (ServerPlayer)player;
            mechanicalCoreCount = serverPlayer.getStats().getValue(Stats.ITEM_USED.get(Tenreincarnationitems.MECHANICAL_CORE.get()));
        }
        // 每个机械核心增加12.5%的进化进度，最多8个（100%）
        return Math.min(100.0, mechanicalCoreCount * 12.5);
    }

    @Override
    public List<Component> getRequirementsForRendering(Player player) {
        List<Component> list = new ArrayList<>();
        list.add(Component.translatable("tensura.evolution_menu.mechanical_core_requirement"));
        return list;
    }

    @Override
    public List<Race> getPreviousEvolutions(Player player) {
        List<Race> list = new ArrayList<>();
        list.add(Tenreincarnationraces.BEFEHLER.get());
        return list;
    }

    @Override
    public List<Race> getNextEvolutions(Player player) {
        List<Race> list = new ArrayList<>();
        list.add(Tenreincarnationraces.HOROU.get());
        return list;
    }

    @Override
    public Race getDefaultEvolution(Player player) {
        return Tenreincarnationraces.HOROU.get();
    }
} 