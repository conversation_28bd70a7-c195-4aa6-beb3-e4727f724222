package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import net.minecraft.ChatFormatting;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.event.entity.ProjectileImpactEvent;
import net.minecraftforge.event.entity.living.LivingAttackEvent;

public class AllSeeingEyeSkill extends Skill {
   public AllSeeingEyeSkill() {
      super(Skill.SkillType.EXTRA);
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      return newEP > 5000.0D;
   }

   public double learningCost() {
      return 100.0D;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return instance.isMastered(living);
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      return !instance.isMastered(entity) ? false : instance.isToggled();
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 10.0D;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (!entity.m_21023_((MobEffect)TensuraMobEffects.ALL_SEEING.get())) {
         entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11862_, SoundSource.PLAYERS, 1.0F, 1.0F);
      }
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (heldTicks % 20 == 0 && SkillHelper.outOfMagicule(entity, instance)) {
         return false;
      } else {
         if (heldTicks % 60 == 0 && heldTicks > 0) {
            this.addMasteryPoint(instance, entity);
         }

         entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.ALL_SEEING.get(), 20, instance.isMastered(entity) ? 1 : 0, false, false, false));
         return true;
      }
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (!instance.isToggled()) {
         if (entity.m_21023_((MobEffect)TensuraMobEffects.ALL_SEEING.get())) {
            entity.m_21195_((MobEffect)TensuraMobEffects.ALL_SEEING.get());
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12049_, SoundSource.PLAYERS, 1.0F, 1.0F);
         }
      }
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      if (SkillHelper.outOfMagicule(entity, this.magiculeCost(entity, instance) * 5.0D)) {
         if (entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237110_("tensura.skill.lack_magicule.toggled_off", new Object[]{instance.getSkill().getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
         }

         instance.setToggled(false);
      } else {
         entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.ALL_SEEING.get(), 200, 0, false, false, false));
      }
   }

   public void onToggleOn(ManasSkillInstance instance, LivingEntity entity) {
      this.onTick(instance, entity);
      entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11767_, SoundSource.PLAYERS, 1.0F, 1.0F);
   }

   public void onToggleOff(ManasSkillInstance instance, LivingEntity entity) {
      if (entity.m_21023_((MobEffect)TensuraMobEffects.ALL_SEEING.get())) {
         entity.m_21195_((MobEffect)TensuraMobEffects.ALL_SEEING.get());
         entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11824_, SoundSource.PLAYERS, 1.0F, 1.0F);
      }
   }

   public void onBeingDamaged(ManasSkillInstance instance, LivingAttackEvent event) {
      if (!event.isCanceled()) {
         if (instance.isToggled()) {
            DamageSource damageSource = event.getSource();
            if (!damageSource.m_19378_()) {
               if (damageSource.m_7640_() != null && damageSource.m_7640_() == damageSource.m_7639_()) {
                  if (!damageSource.m_19387_()) {
                     LivingEntity entity = event.getEntity();
                     if (entity.m_217043_().m_188503_(10) == 1) {
                        TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123766_, 1.0D);
                        entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12318_, SoundSource.PLAYERS, 2.0F, 1.0F);
                        event.setCanceled(true);
                        if (SkillUtils.canNegateDodge(entity, damageSource)) {
                           event.setCanceled(false);
                        }

                     }
                  }
               }
            }
         }
      }
   }

   public void onProjectileHit(ManasSkillInstance instance, LivingEntity entity, ProjectileImpactEvent event) {
      if (instance.isToggled()) {
         if (!SkillUtils.isProjectileAlwaysHit(event.getProjectile())) {
            if (entity.m_217043_().m_188503_(10) == 1) {
               TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123766_, 1.0D);
               entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12318_, SoundSource.PLAYERS, 2.0F, 1.0F);
               event.setCanceled(true);
            }
         }
      }
   }
}
