package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import java.util.Objects;
import java.util.UUID;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.player.Player;

public class MultilayerBarrierSkill extends Skill {
   protected static final UUID MULTILAYER = UUID.fromString("2c03b682-5705-11ee-8c99-0242ac120002");

   public MultilayerBarrierSkill() {
      super(Skill.SkillType.EXTRA);
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 0.0D;
   }

   public void onToggleOff(ManasSkillInstance instance, LivingEntity entity) {
      AttributeInstance attribute = entity.m_21051_((Attribute)TensuraAttributeRegistry.BARRIER.get());
      if (attribute != null) {
         if (attribute.m_22111_(MULTILAYER) != null) {
            attribute.m_22127_(MULTILAYER);
         }

      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (!SkillHelper.outOfMagicule(entity, instance)) {
         AttributeInstance attributeInstance = (AttributeInstance)Objects.requireNonNull(entity.m_21051_((Attribute)TensuraAttributeRegistry.BARRIER.get()));
         if (attributeInstance.m_22111_(MULTILAYER) != null) {
            attributeInstance.m_22127_(MULTILAYER);
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11738_, SoundSource.PLAYERS, 1.0F, 1.0F);
         } else {
            this.addMasteryPoint(instance, entity);
            instance.setCoolDown(10);
            double barrierPoints = (double)entity.m_21233_() * 1.5D;
            attributeInstance.m_22125_(new AttributeModifier(MULTILAYER, "Multilayer Barrier", barrierPoints, Operation.ADDITION));
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11736_, SoundSource.PLAYERS, 1.0F, 1.0F);
         }

      }
   }
}
