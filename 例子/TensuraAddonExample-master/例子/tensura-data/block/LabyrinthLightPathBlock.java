package com.github.manasmods.tensura.block;

import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import net.minecraft.core.Direction;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.SlabBlock;
import net.minecraft.world.level.block.SoundType;
import net.minecraft.world.level.block.StairBlock;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.properties.Half;
import net.minecraft.world.level.block.state.properties.SlabType;
import net.minecraft.world.level.block.state.properties.StairsShape;
import net.minecraft.world.level.material.Material;

public class LabyrinthLightPathBlock extends SimpleBlock {
   public LabyrinthLightPathBlock() {
      super(Material.f_76275_, (properties) -> {
         return properties.m_60971_((blockState, blockGetter, blockPos) -> {
            return false;
         }).m_222994_().m_60918_(SoundType.f_154654_).m_60955_().m_60953_((state) -> {
            return 15;
         }).m_60913_(-1.0F, 3600000.0F);
      });
   }

   public boolean m_6104_(BlockState state, BlockState stateFrom, Direction direction) {
      if (!stateFrom.m_60713_(this) && !stateFrom.m_60713_((Block)TensuraBlocks.LABYRINTH_PRAYING_PATH.get())) {
         if (stateFrom.m_60734_() == TensuraBlocks.LABYRINTH_LIGHT_PATH_SLAB.get() && this.isInvisibleToGlassSlab(state, stateFrom, direction)) {
            return true;
         } else {
            return stateFrom.m_60734_() == TensuraBlocks.LABYRINTH_LIGHT_PATH_STAIRS.get() && this.isInvisibleToGlassStairs(state, stateFrom, direction) ? true : super.m_6104_(state, stateFrom, direction);
         }
      } else {
         return true;
      }
   }

   private boolean isInvisibleToGlassSlab(BlockState state, BlockState stateFrom, Direction direction) {
      SlabType typeFrom = (SlabType)stateFrom.m_61143_(SlabBlock.f_56353_);
      if (typeFrom == SlabType.DOUBLE) {
         return true;
      } else if (direction == Direction.UP && typeFrom != SlabType.TOP) {
         return true;
      } else {
         return direction == Direction.DOWN && typeFrom != SlabType.BOTTOM;
      }
   }

   private boolean isInvisibleToGlassStairs(BlockState state, BlockState stateFrom, Direction direction) {
      Half halfFrom = (Half)stateFrom.m_61143_(StairBlock.f_56842_);
      Direction facingFrom = (Direction)stateFrom.m_61143_(StairBlock.f_56841_);
      StairsShape shapeFrom = (StairsShape)stateFrom.m_61143_(StairBlock.f_56843_);
      if (direction == Direction.UP && halfFrom == Half.BOTTOM) {
         return true;
      } else if (direction == Direction.DOWN && halfFrom == Half.TOP) {
         return true;
      } else if (facingFrom == direction.m_122424_() && shapeFrom != StairsShape.OUTER_LEFT && shapeFrom != StairsShape.OUTER_RIGHT) {
         return true;
      } else if (facingFrom.m_122428_() == direction && shapeFrom == StairsShape.INNER_RIGHT) {
         return true;
      } else {
         return facingFrom.m_122427_() == direction && shapeFrom == StairsShape.INNER_LEFT;
      }
   }
}
