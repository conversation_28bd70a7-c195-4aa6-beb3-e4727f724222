package com.github.b4ndithelps.tennogamenolife.race.Fantasuma;

import com.github.b4ndithelps.tennogamenolife.registry.race.Tenreincarnationraces;
import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.github.manasmods.tensura.util.JumpPowerHelper;
import com.mojang.datafixers.util.Pair;
import net.minecraft.world.entity.player.Player;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Nullable;

/**
 * 幻想种
 */
public class FantasumaRace extends Race {

    // 种族基础属性
    private double baseHealth = 70.0;          // 基础生命值
    private double baseAttackDamage = 3.5;     // 基础攻击伤害
    private double baseAttackSpeed = 4.0;      // 基础攻击速度
    private double knockbackResistance = 0.2;   // 击退抗性
    private double jumpHeight = 1.2;           // 跳跃高度
    private double movementSpeed = 0.12;       // 移动速度
    private double sprintSpeed = 0.18;         // 冲刺速度
    private double auraMin = 1000.0;            // 最小灵气值
    private double auraMax = 2000.0;           // 最大灵气值
    private double startingMagiculeMin = 400.0; // 最小初始魔素值
    private double startingMagiculeMax = 900.0; // 最大初始魔素值

    private float playerSize = 2.5f;           // 玩家大小

    public FantasumaRace() {
        super(Difficulty.EASY);
    }

    @Override
    public double getBaseHealth() {
        return baseHealth;
    }

    @Override
    public float getPlayerSize() {
        return playerSize;
    }

    @Override
    public double getBaseAttackDamage() {
        return baseAttackDamage;
    }

    @Override
    public double getBaseAttackSpeed() {
        return baseAttackSpeed;
    }

    @Override
    public double getKnockbackResistance() {
        return knockbackResistance;
    }

    @Override
    public double getJumpHeight() {
        return JumpPowerHelper.defaultPlayer(jumpHeight);
    }

    @Override
    public double getMovementSpeed() {
        return movementSpeed;
    }

    @Override
    public double getSprintSpeed() {
        return sprintSpeed;
    }

    @Override
    public Pair<Double, Double> getBaseAuraRange() {
        return Pair.of(auraMin, auraMax);
    }

    @Override
    public Pair<Double, Double> getBaseMagiculeRange() {
        return Pair.of(startingMagiculeMin, startingMagiculeMax);
    }

    @Override
    public List<TensuraSkill> getIntrinsicSkills(Player player) {
        List<TensuraSkill> list = new ArrayList<>();
        list.add(IntrinsicSkills.DRAGON_MODE.get());
        return list;
    }

    @Override
    public boolean isMajin() {
        return false;
    }

    @Override
    public boolean isSpiritual() {
        return false;
    }

    @Override
    public boolean isDivine() {
        return false;
    }

    @Override
    public void raceAbility(Player player) {
        // 种族能力将在后续添加
    }

    @Override
    public List<Race> getNextEvolutions(Player player) {
        List<Race> list = new ArrayList<>();
        // 进化路径将在后续添加
        return list;
    }

    @Nullable
    @Override
    public Race getDefaultEvolution(Player player) {
        return null;
    }

    @Nullable
    @Override
    public Race getAwakeningEvolution(Player player) {
        return null;
    }

    @Nullable
    @Override
    public Race getHarvestFestivalEvolution(Player player) {
        return null;
    }
} 