package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.tensura.ability.ISpatialStorage;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.ability.skill.intrinsic.AbsorbDissolveSkill;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.magic.breath.BreathEntity;
import com.github.manasmods.tensura.event.SkillPlunderEvent;
import com.github.manasmods.tensura.menu.container.SpatialStorageContainer;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.RequestFxSpawningPacket;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.items.TensuraMaterialItems;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.github.manasmods.tensura.util.TensuraAdvancementsHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import com.github.manasmods.tensura.world.TensuraGameRules;
import java.util.Iterator;
import java.util.List;
import java.util.function.Predicate;
import net.minecraft.ChatFormatting;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.stats.Stats;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.effect.MobEffectCategory;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.Level;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.network.PacketDistributor;
import org.jetbrains.annotations.NotNull;

public class GluttonySkill extends Skill implements ISpatialStorage {
   private static final String CORROSION = "8c4fec2d-6913-435e-9bfd-a111b8eadf62";

   public GluttonySkill() {
      super(Skill.SkillType.UNIQUE);
      this.addHeldAttributeModifier(Attributes.f_22279_, "8c4fec2d-6913-435e-9bfd-a111b8eadf62", -0.5D, Operation.MULTIPLY_TOTAL);
   }

   public double getObtainingEpCost() {
      return 100000.0D;
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      SkillStorage storage = SkillAPI.getSkillsFrom(entity);
      return storage.getSkill((ManasSkill)UniqueSkills.STARVED.get()).isEmpty() ? false : (Boolean)storage.getSkill((ManasSkill)UniqueSkills.PREDATOR.get()).map((instance) -> {
         return instance.isMastered(entity);
      }).orElse(false);
   }

   public int getMasteryOnEPAcquirement(Player entity) {
      return 0;
   }

   public int getMaxMastery() {
      return 1500;
   }

   public double learningCost() {
      return 2000.0D;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity entity) {
      return true;
   }

   public int modes() {
      return 7;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      if (reverse) {
         return instance.getMode() == 1 ? 7 : instance.getMode() - 1;
      } else {
         return instance.getMode() == 7 ? 1 : instance.getMode() + 1;
      }
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.predator.predation");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.predator.stomach");
         break;
      case 3:
         var10000 = Component.m_237115_("tensura.skill.mode.predator.mimicry");
         break;
      case 4:
         var10000 = Component.m_237115_("tensura.skill.mode.predator.isolation");
         break;
      case 5:
         var10000 = Component.m_237115_("tensura.skill.mode.starved.corrosion");
         break;
      case 6:
         var10000 = Component.m_237115_("tensura.skill.mode.starved.receive");
         break;
      case 7:
         var10000 = Component.m_237115_("tensura.skill.mode.starved.provide");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public boolean canIgnoreCoolDown(ManasSkillInstance instance, LivingEntity entity) {
      return instance.getMode() == 1 && entity.m_6144_();
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      double var10000;
      switch(instance.getMode()) {
      case 4:
         var10000 = 100.0D;
         break;
      case 5:
         var10000 = 200.0D;
         break;
      default:
         var10000 = 0.0D;
      }

      return var10000;
   }

   public void addHeldAttributeModifiers(ManasSkillInstance instance, LivingEntity entity) {
      if (instance.getMode() == 5) {
         super.addHeldAttributeModifiers(instance, entity);
      }
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (instance.onCoolDown()) {
         return false;
      } else {
         switch(instance.getMode()) {
         case 1:
            if (entity.m_6144_()) {
               return false;
            }

            CompoundTag tag = instance.getOrCreateTag();
            if (tag.m_128459_("range") < 3.0D) {
               tag.m_128347_("range", 10.0D);
            }

            if (heldTicks % 60 == 0 && heldTicks > 0) {
               this.addMasteryPoint(instance, entity);
            }

            BreathEntity.spawnPredationMist((EntityType)TensuraEntityTypes.GLUTTONY_MIST.get(), entity, instance, this.magiculeCost(entity, instance), (float)tag.m_128459_("range"), tag.m_128451_("blockMode"), true);
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11705_, SoundSource.PLAYERS, 1.0F, 1.0F);
            return true;
         case 5:
            return this.corrosion(instance, entity, heldTicks);
         default:
            return false;
         }
      }
   }

   public void onScroll(ManasSkillInstance instance, LivingEntity entity, double delta) {
      if (instance.getMode() == 1) {
         CompoundTag tag = instance.getOrCreateTag();
         double newRange = tag.m_128459_("range") + delta;
         int maxRange = instance.isMastered(entity) ? 15 : 10;
         if (newRange > (double)maxRange) {
            newRange = 3.0D;
         } else if (newRange < 3.0D) {
            newRange = (double)maxRange;
         }

         if (tag.m_128459_("range") != newRange) {
            tag.m_128347_("range", newRange);
            if (entity instanceof Player) {
               Player player = (Player)entity;
               player.m_5661_(Component.m_237110_("tensura.skill.range", new Object[]{newRange}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_AQUA)), true);
            }

            instance.markDirty();
         }

      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      Level level = entity.m_9236_();
      CompoundTag tag = instance.getOrCreateTag();
      Player player;
      switch(instance.getMode()) {
      case 1:
         instance.getOrCreateTag().m_128405_("BreathEntity", 0);
         if (entity.m_6144_() && entity instanceof Player) {
            Player player = (Player)entity;
            byte newMode;
            switch(tag.m_128451_("blockMode")) {
            case 1:
               newMode = 2;
               player.m_5661_(Component.m_237110_("tensura.skill.predator.block_mode.blocks", new Object[]{this.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_AQUA)), true);
               break;
            case 2:
               newMode = 3;
               player.m_5661_(Component.m_237110_("tensura.skill.predator.block_mode.fluid", new Object[]{this.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_AQUA)), true);
               break;
            case 3:
               newMode = 4;
               player.m_5661_(Component.m_237110_("tensura.skill.predator.block_mode.all", new Object[]{this.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_AQUA)), true);
               break;
            default:
               newMode = 1;
               player.m_5661_(Component.m_237110_("tensura.skill.predator.block_mode.none", new Object[]{this.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_AQUA)), true);
            }

            tag.m_128405_("blockMode", newMode);
         }

         instance.markDirty();
         break;
      case 2:
         this.openSpatialStorage(entity, instance);
         break;
      case 3:
         SkillHelper.comingSoonMessage(entity, "Mimicry");
         break;
      case 4:
         ItemStack itemStack = entity.m_21205_();
         entity.m_21011_(InteractionHand.MAIN_HAND, true);
         if (itemStack.m_41619_()) {
            LivingEntity target = SkillHelper.getTargetingEntity(entity, 3.0D, false);
            boolean success;
            Predicate predicate;
            if (target != null) {
               success = TensuraEffectsCapability.getSeverance(target) > 0.0D;
               TensuraEffectsCapability.getFrom(target).ifPresent((cap) -> {
                  cap.setSeveranceAmount(0.0D);
               });
               predicate = (effect) -> {
                  return effect.m_19483_() == MobEffectCategory.HARMFUL;
               };
               success = success || SkillHelper.removePredicateEffect(target, predicate, this.magiculeCost(entity, instance));
               if (success) {
                  TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123749_, 2.0D);
               }
            } else {
               success = TensuraEffectsCapability.getSeverance(entity) > 0.0D;
               TensuraEffectsCapability.getFrom(entity).ifPresent((cap) -> {
                  cap.setSeveranceAmount(0.0D);
               });
               predicate = (effect) -> {
                  return effect.m_19483_() == MobEffectCategory.HARMFUL;
               };
               success = success || SkillHelper.removePredicateEffect(entity, predicate, this.magiculeCost(entity, instance));
               if (success) {
                  TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123749_, 2.0D);
               }
            }

            if (success) {
               instance.setCoolDown(instance.isMastered(entity) ? 5 : 3);
               level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12275_, SoundSource.PLAYERS, 1.0F, 1.0F);
            }
         } else {
            AbsorbDissolveSkill.Dissolving[] var16 = AbsorbDissolveSkill.Dissolving.values();
            int var15 = var16.length;

            for(int var19 = 0; var19 < var15; ++var19) {
               AbsorbDissolveSkill.Dissolving dissolving = var16[var19];
               if (dissolving.getItem().equals(itemStack.m_41720_())) {
                  itemStack.m_41774_(1);
                  if (entity instanceof ServerPlayer) {
                     ServerPlayer serverPlayer = (ServerPlayer)entity;
                     if (itemStack.m_41720_().equals(TensuraMaterialItems.SLIME_IN_A_BUCKET.get())) {
                        TensuraAdvancementsHelper.grant(serverPlayer, TensuraAdvancementsHelper.Advancements.TRAITOR);
                     }

                     serverPlayer.m_36246_(Stats.f_12982_.m_12902_(itemStack.m_41720_()));
                  }

                  if (entity instanceof Player) {
                     player = (Player)entity;
                     TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
                        cap.setMagicule(cap.getMagicule() + (double)(dissolving.getMagicule() * 2));
                        if (cap.getMagicule() > player.m_21133_((Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get())) {
                           cap.setMagicule(player.m_21133_((Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get()));
                        }

                     });
                  }

                  if (dissolving.getHeal() > 0.0F) {
                     entity.m_5634_(dissolving.getHeal());
                  }

                  level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12321_, SoundSource.PLAYERS, 1.0F, 1.0F);
               }
            }
         }
      case 5:
      default:
         break;
      case 6:
         LivingEntity living = SkillHelper.getTargetingEntity(entity, 5.0D, false);
         if (living == null || !living.m_6084_()) {
            return;
         }

         if (!SkillHelper.isSubordinate(entity, living)) {
            return;
         }

         if (living.m_6095_().m_204039_(TensuraTags.EntityTypes.NO_SKILL_PLUNDER)) {
            return;
         }

         List<ManasSkillInstance> collection = SkillAPI.getSkillsFrom(living).getLearnedSkills().stream().filter((sub) -> {
            return StarvedSkill.canGain(sub.getSkill());
         }).toList();
         Iterator var7 = collection.iterator();

         while(var7.hasNext()) {
            ManasSkillInstance targetInstance = (ManasSkillInstance)var7.next();
            if (!targetInstance.isTemporarySkill() && targetInstance.getMastery() >= 0 && targetInstance.getSkill() != this) {
               SkillPlunderEvent event = new SkillPlunderEvent(living, entity, false, targetInstance.getSkill());
               if (!MinecraftForge.EVENT_BUS.post(event) && SkillUtils.learnSkill(entity, event.getSkill(), instance.getRemoveTime())) {
                  if (entity instanceof Player) {
                     player = (Player)entity;
                     player.m_5661_(Component.m_237110_("tensura.skill.acquire", new Object[]{event.getSkill().getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
                  }

                  level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12275_, SoundSource.PLAYERS, 1.0F, 1.0F);
                  ((ServerLevel)level).m_8767_(ParticleTypes.f_175828_, entity.m_20185_(), entity.m_20186_() + (double)entity.m_20206_() / 2.0D, entity.m_20189_(), 20, 0.08D, 0.08D, 0.08D, 0.15D);
               }
            }
         }

         return;
      case 7:
         SkillHelper.comingSoonMessage(entity, "Provide");
      }

   }

   private boolean corrosion(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (heldTicks % 20 == 0 && SkillHelper.outOfMagicule(entity, instance)) {
         return false;
      } else {
         if (heldTicks % 100 == 0 && heldTicks > 0) {
            this.addMasteryPoint(instance, entity);
         }

         Level level = entity.m_9236_();
         level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12031_, SoundSource.PLAYERS, 1.0F, 1.0F);
         if (heldTicks % 10 == 0) {
            TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
               return entity;
            }), new RequestFxSpawningPacket(new ResourceLocation("tensura:gluttony_corrosion"), entity.m_19879_(), 0.0D, 1.0D, 0.0D, true));
         }

         if (heldTicks % 10 == 0) {
            List<LivingEntity> list = level.m_6443_(LivingEntity.class, entity.m_20191_().m_82400_(5.0D), (living) -> {
               return !living.m_7306_(entity) && living.m_6084_() && !living.m_7307_(entity);
            });
            if (!list.isEmpty()) {
               Iterator var6 = list.iterator();

               while(true) {
                  CompoundTag tag;
                  double EP;
                  while(true) {
                     LivingEntity target;
                     do {
                        while(true) {
                           do {
                              do {
                                 do {
                                    if (!var6.hasNext()) {
                                       return true;
                                    }

                                    target = (LivingEntity)var6.next();
                                 } while(!target.m_6469_(this.sourceWithMP(TensuraDamageSources.corrosion(entity), entity, instance), 10.0F));
                              } while(!target.m_21224_());

                              if (!target.m_6095_().m_204039_(TensuraTags.EntityTypes.NO_SKILL_PLUNDER)) {
                                 List<ManasSkillInstance> targetSkills = SkillAPI.getSkillsFrom(target).getLearnedSkills().stream().filter((skillInstance) -> {
                                    return StarvedSkill.canGain(skillInstance.getSkill());
                                 }).toList();
                                 Iterator var9 = targetSkills.iterator();

                                 while(var9.hasNext()) {
                                    ManasSkillInstance targetInstance = (ManasSkillInstance)var9.next();
                                    if (!targetInstance.isTemporarySkill() && targetInstance.getMastery() >= 0 && targetInstance.getSkill() != this) {
                                       SkillPlunderEvent event = new SkillPlunderEvent(target, entity, false, targetInstance.getSkill());
                                       if (!MinecraftForge.EVENT_BUS.post(event) && SkillUtils.learnSkill(entity, event.getSkill(), instance.getRemoveTime()) && entity instanceof Player) {
                                          Player player = (Player)entity;
                                          player.m_5661_(Component.m_237110_("tensura.skill.acquire", new Object[]{event.getSkill().getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
                                       }
                                    }
                                 }
                              }
                           } while(instance.isTemporarySkill());

                           tag = instance.getOrCreateTag();
                           CompoundTag predationList;
                           if (tag.m_128441_("predationList")) {
                              predationList = (CompoundTag)tag.m_128423_("predationList");
                              if (predationList == null) {
                                 continue;
                              }

                              String targetID = EntityType.m_20613_(target.m_6095_()).toString();
                              if (predationList.m_128441_(targetID)) {
                                 continue;
                              }

                              predationList.m_128379_(targetID, true);
                              break;
                           }

                           predationList = new CompoundTag();
                           predationList.m_128379_(EntityType.m_20613_(target.m_6095_()).toString(), true);
                           tag.m_128365_("predationList", predationList);
                           break;
                        }
                     } while(target.m_6095_().m_204039_(TensuraTags.EntityTypes.NO_EP_PLUNDER));

                     EP = Math.min(SkillUtils.getEPGain(target, entity), (Double)TensuraConfig.INSTANCE.skillsConfig.maximumEPSteal.get());
                     if (target instanceof Player) {
                        Player playerTarget = (Player)target;
                        if (!TensuraGameRules.canEpSteal(target.m_9236_())) {
                           break;
                        }

                        int minEP = TensuraGameRules.getMinEp(level);
                        if (minEP > 0) {
                           EP -= (double)minEP;
                        }

                        if (EP <= 0.0D) {
                           continue;
                        }

                        SkillHelper.gainMaxMP(entity, EP * 0.4D);
                        SkillHelper.gainMaxAP(entity, EP * 0.4D);
                        TensuraEPCapability.setSkippingEPDrop(target, true);
                        SkillHelper.gainMP(entity, EP * 0.4D, false);
                        SkillHelper.gainAP(entity, EP * 0.4D, false);
                        TensuraPlayerCapability.getFrom(playerTarget).ifPresent((cap) -> {
                           cap.setBaseMagicule((double)minEP / 2.0D, playerTarget);
                           cap.setBaseAura((double)minEP / 2.0D, playerTarget);
                        });
                        TensuraPlayerCapability.sync(playerTarget);
                        break;
                     }

                     SkillHelper.gainMaxMP(entity, EP * 0.4D);
                     SkillHelper.gainMaxAP(entity, EP * 0.4D);
                     TensuraEPCapability.setSkippingEPDrop(target, true);
                     SkillHelper.gainMP(entity, EP * 0.4D, false);
                     SkillHelper.gainAP(entity, EP * 0.4D, false);
                     SkillHelper.reduceEP(target, entity, 1.0D, true, true);
                     break;
                  }

                  tag.m_128347_("storedMP", tag.m_128459_("storedMP") + EP * 0.1D);
                  tag.m_128347_("storedAP", tag.m_128459_("storedAP") + EP * 0.1D);
                  instance.markDirty();
               }
            }
         }

         return true;
      }
   }

   @NotNull
   public SpatialStorageContainer getSpatialStorage(ManasSkillInstance instance) {
      SpatialStorageContainer container = new SpatialStorageContainer(81, 666);
      container.m_7797_(instance.getOrCreateTag().m_128437_("SpatialStorage", 10));
      return container;
   }
}
