package com.github.manasmods.tensura.entity.magic.barrier;

import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.level.Level;

public class MagicEngineBarrierEntity extends BarrierEntity {
   public MagicEngineBarrierEntity(Level level) {
      this((EntityType)TensuraEntityTypes.MAGIC_ENGINE_BARRIER.get(), level);
      this.f_19811_ = false;
      this.f_19794_ = true;
   }

   public MagicEngineBarrierEntity(EntityType<? extends MagicEngineBarrierEntity> entityType, Level level) {
      super(entityType, level);
   }

   public boolean canWalkThrough() {
      return true;
   }

   public boolean blockBuilding() {
      return false;
   }

   public boolean isMultipartEntity() {
      return false;
   }

   public boolean m_6469_(DamageSource pSource, float pAmount) {
      return false;
   }
}
